"""Pydantic 模型"""

from .cookie import (
    CookieDeleteRequest,
    CookieModel,
    CookieOperationResult,
    CookieQueryRequest,
    SetCookiesBatchAction,
)
from .mq_message import (
    AppendFilePathMessage,
    AppendLogMessage,
    AppendScreenshotPathMessage,
    AppendVideoPathMessage,
    DatabaseMessage,
    DatabaseOperationType,
    MessageResponse,
    UpdateErrorMessage,
    UpdateFilePathsMessage,
    UpdateResultMessage,
    UpdateSeleniumSessionMessage,
    UpdateStatusMessage,
)
from .script import (
    ScriptExecuteRequest,
    ScriptExecuteResponse,
    ScriptResultResponse,
    ScriptStatusResponse,
    ScriptTerminateResponse,
)
from .task import (
    TaskExecuteRequest,
    TaskExecuteResponse,
    TaskResultResponse,
    TaskStatusResponse,
    TaskTerminateResponse,
)

__all__ = [
    # <PERSON>ie schemas
    "CookieDeleteRequest",
    "<PERSON>ieModel",
    "<PERSON>ieOperationResult",
    "CookieQueryRequest",
    "SetCookiesBatchAction",
    # MQ Message schemas
    "AppendLogMessage",
    "AppendScreenshotPathMessage",
    "AppendVideoPathMessage",
    "AppendFilePathMessage",
    "DatabaseMessage",
    "DatabaseOperationType",
    "MessageResponse",
    "UpdateErrorMessage",
    "UpdateFilePathsMessage",
    "UpdateResultMessage",
    "UpdateSeleniumSessionMessage",
    "UpdateStatusMessage",
    # Script schemas
    "ScriptExecuteRequest",
    "ScriptExecuteResponse",
    "ScriptResultResponse",
    "ScriptStatusResponse",
    "ScriptTerminateResponse",
    # Task schemas
    "TaskExecuteRequest",
    "TaskExecuteResponse",
    "TaskResultResponse",
    "TaskStatusResponse",
    "TaskTerminateResponse",
]
