"""
任务相关的 Pydantic 模型

定义 API 请求和响应的数据结构
"""

from datetime import datetime

from pydantic import BaseModel, Field, field_validator

from app.config.enums import ExecutionStatus


class TaskExecuteRequest(BaseModel):
    """创建任务的执行请求模型"""

    task: str = Field(..., description="用户输入的任务描述")
    username: str | None = Field(None, description="可选的登录用户名")
    password: str | None = Field(None, description="可选的登录密码")
    provider_type: str | None = Field(
        None,
        description="可选的云服务提供商类型，支持: ecloud(移动云), baidu_cloud(百度云), huawei_cloud(华为云), alibaba_cloud(阿里云), default(默认)",
    )
    cookies: list[dict] | None = Field(None, description="可选的 Cookie 列表，用于在浏览器会话中设置认证信息")

    @field_validator("task")
    @classmethod
    def validate_task(cls, value: str) -> str:
        """验证任务描述不能为空"""
        if not value or not value.strip():
            raise ValueError("任务描述不能为空")
        return value.strip()

    @field_validator("provider_type")
    @classmethod
    def validate_provider_type(cls, value: str | None) -> str | None:
        """验证云服务提供商类型"""
        if value is None:
            return value

        # 支持的云服务提供商类型
        valid_types = ["ecloud", "baidu_cloud", "huawei_cloud", "alibaba_cloud", "default"]

        if value.lower() not in valid_types:
            raise ValueError(f"不支持的云服务提供商类型: {value}，支持的类型: {', '.join(valid_types)}")

        return value.lower()


class TaskExecuteResponse(BaseModel):
    """创建任务的执行响应模型"""

    ticket_id: str  # 生成的任务ID
    status: str  # 当前状态
    message: str  # 状态描述信息


class TaskResultResponse(BaseModel):
    """获取任务结果的响应模型"""

    ticket_id: str  # 任务ID
    status: ExecutionStatus | None = None  # 任务状态
    step_log: str | None = None  # 任务执行步骤日志
    result: list | None = None  # 成功时的结果数据
    error_message: str | None = None  # 失败时的错误信息
    selenium_session_id: str | None = None  # Selenium会话ID
    replica_id: str | None = None  # 执行该任务的副本ID
    created_at: datetime | None = None  # 创建时间
    updated_at: datetime | None = None  # 更新时间

    # 文件存储相关字段
    video_file_paths: list[str] | None = None  # 视频文件路径列表
    screenshot_file_paths: list[str] | None = None  # 截图文件路径列表
    download_file_paths: list[str] | None = None  # 下载文件路径列表


class TaskStatusResponse(BaseModel):
    """获取任务状态的响应模型（轻量级）"""

    ticket_id: str  # 任务ID
    status: ExecutionStatus | None = None  # 任务状态
    replica_id: str | None = None  # 执行该任务的副本ID


class TaskTerminateResponse(BaseModel):
    """终止任务的响应模型"""

    ticket_id: str  # 任务ID
    message: str  # 操作结果描述
    success: bool  # 是否成功终止


class TaskFilesResponse(BaseModel):
    """获取任务文件的响应模型"""

    ticket_id: str = Field(..., description="任务ID")
    video_urls: list[str] = Field(default_factory=list, description="视频文件URL列表")
    screenshots_urls: list[str] = Field(default_factory=list, description="截图文件URL列表")
    file_urls: list[str] = Field(default_factory=list, description="下载文件URL列表")
