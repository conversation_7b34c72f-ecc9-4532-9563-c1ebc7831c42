#!/usr/bin/env python3
"""
告警系统
监控RabbitMQ消息处理失败和数据异常，提供告警机制
"""

import json
from datetime import datetime, timedelta
from enum import Enum

from loguru import logger
from pydantic import BaseModel


class AlertLevel(str, Enum):
    """告警级别"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class AlertType(str, Enum):
    """告警类型"""
    MESSAGE_PROCESSING_FAILED = "MESSAGE_PROCESSING_FAILED"
    DATA_INTEGRITY_ISSUE = "DATA_INTEGRITY_ISSUE"
    SELENIUM_SESSION_MISSING = "SELENIUM_SESSION_MISSING"
    STARTED_AT_MISSING = "STARTED_AT_MISSING"
    FILE_PATH_INCONSISTENT = "FILE_PATH_INCONSISTENT"
    TASK_TIMEOUT = "TASK_TIMEOUT"


class Alert(BaseModel):
    """告警模型"""
    alert_id: str
    alert_type: AlertType
    level: AlertLevel
    title: str
    message: str
    ticket_id: str | None = None
    timestamp: datetime
    metadata: dict = {}


class AlertSystem:
    """告警系统"""

    def __init__(self):
        self.alerts: list[Alert] = []
        self.alert_handlers = {
            AlertLevel.INFO: self._handle_info_alert,
            AlertLevel.WARNING: self._handle_warning_alert,
            AlertLevel.ERROR: self._handle_error_alert,
            AlertLevel.CRITICAL: self._handle_critical_alert,
        }

    def create_alert(
        self,
        alert_type: AlertType,
        level: AlertLevel,
        title: str,
        message: str,
        ticket_id: str | None = None,
        metadata: dict | None = None
    ) -> Alert:
        """创建告警"""
        alert = Alert(
            alert_id=f"{alert_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{ticket_id or 'SYSTEM'}",
            alert_type=alert_type,
            level=level,
            title=title,
            message=message,
            ticket_id=ticket_id,
            timestamp=datetime.now(),
            metadata=metadata or {}
        )

        self.alerts.append(alert)
        self._process_alert(alert)
        return alert

    def _process_alert(self, alert: Alert):
        """处理告警"""
        handler = self.alert_handlers.get(alert.level)
        if handler:
            handler(alert)

    def _handle_info_alert(self, alert: Alert):
        """处理信息级别告警"""
        logger.info(f"[ALERT-INFO] {alert.title}: {alert.message}")

    def _handle_warning_alert(self, alert: Alert):
        """处理警告级别告警"""
        logger.warning(f"[ALERT-WARNING] {alert.title}: {alert.message}")
        # 可以在这里添加更多处理逻辑，如发送通知

    def _handle_error_alert(self, alert: Alert):
        """处理错误级别告警"""
        logger.error(f"[ALERT-ERROR] {alert.title}: {alert.message}")
        # 可以在这里添加更多处理逻辑，如发送邮件、Slack通知等

    def _handle_critical_alert(self, alert: Alert):
        """处理严重级别告警"""
        logger.critical(f"[ALERT-CRITICAL] {alert.title}: {alert.message}")
        # 可以在这里添加更多处理逻辑，如立即通知、自动修复等

    def get_recent_alerts(self, hours: int = 24) -> list[Alert]:
        """获取最近的告警"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alerts if alert.timestamp >= cutoff_time]

    def get_alerts_by_type(self, alert_type: AlertType, hours: int = 24) -> list[Alert]:
        """按类型获取告警"""
        recent_alerts = self.get_recent_alerts(hours)
        return [alert for alert in recent_alerts if alert.alert_type == alert_type]

    def get_alerts_by_ticket(self, ticket_id: str, hours: int = 24) -> list[Alert]:
        """按任务ID获取告警"""
        recent_alerts = self.get_recent_alerts(hours)
        return [alert for alert in recent_alerts if alert.ticket_id == ticket_id]

    def generate_alert_summary(self, hours: int = 24) -> dict:
        """生成告警摘要"""
        recent_alerts = self.get_recent_alerts(hours)

        summary = {
            "total_alerts": len(recent_alerts),
            "by_level": {},
            "by_type": {},
            "recent_critical": [],
            "timestamp": datetime.now().isoformat()
        }

        # 按级别统计
        for level in AlertLevel:
            count = len([a for a in recent_alerts if a.level == level])
            summary["by_level"][level.value] = count

        # 按类型统计
        for alert_type in AlertType:
            count = len([a for a in recent_alerts if a.alert_type == alert_type])
            summary["by_type"][alert_type.value] = count

        # 最近的严重告警
        critical_alerts = [a for a in recent_alerts if a.level == AlertLevel.CRITICAL]
        summary["recent_critical"] = [
            {
                "alert_id": a.alert_id,
                "title": a.title,
                "ticket_id": a.ticket_id,
                "timestamp": a.timestamp.isoformat()
            }
            for a in critical_alerts[-5:]  # 最近5个
        ]

        return summary


# 全局告警系统实例
alert_system = AlertSystem()


def create_message_processing_alert(ticket_id: str, operation_type: str, error_message: str):
    """创建消息处理失败告警"""
    alert_system.create_alert(
        alert_type=AlertType.MESSAGE_PROCESSING_FAILED,
        level=AlertLevel.ERROR,
        title="RabbitMQ消息处理失败",
        message=f"任务 {ticket_id} 的 {operation_type} 消息处理失败: {error_message}",
        ticket_id=ticket_id,
        metadata={
            "operation_type": operation_type,
            "error_message": error_message
        }
    )


def create_data_integrity_alert(ticket_id: str, issue_type: str, details: str):
    """创建数据完整性问题告警"""
    level = AlertLevel.WARNING
    if issue_type in ["started_at_missing", "selenium_session_missing"]:
        level = AlertLevel.ERROR

    alert_system.create_alert(
        alert_type=AlertType.DATA_INTEGRITY_ISSUE,
        level=level,
        title="数据完整性问题",
        message=f"任务 {ticket_id} 存在 {issue_type} 问题: {details}",
        ticket_id=ticket_id,
        metadata={
            "issue_type": issue_type,
            "details": details
        }
    )


def create_selenium_session_missing_alert(ticket_id: str):
    """创建Selenium会话缺失告警"""
    alert_system.create_alert(
        alert_type=AlertType.SELENIUM_SESSION_MISSING,
        level=AlertLevel.ERROR,
        title="Selenium会话ID缺失",
        message=f"任务 {ticket_id} 的selenium_session_id字段缺失",
        ticket_id=ticket_id
    )


def create_started_at_missing_alert(ticket_id: str):
    """创建started_at字段缺失告警"""
    alert_system.create_alert(
        alert_type=AlertType.STARTED_AT_MISSING,
        level=AlertLevel.ERROR,
        title="任务开始时间缺失",
        message=f"任务 {ticket_id} 的started_at字段缺失",
        ticket_id=ticket_id
    )


def create_file_path_inconsistent_alert(ticket_id: str, db_count: int, actual_count: int, file_type: str):
    """创建文件路径不一致告警"""
    alert_system.create_alert(
        alert_type=AlertType.FILE_PATH_INCONSISTENT,
        level=AlertLevel.WARNING,
        title="文件路径不一致",
        message=f"任务 {ticket_id} 的{file_type}文件数量不一致: 数据库{db_count}个，实际{actual_count}个",
        ticket_id=ticket_id,
        metadata={
            "file_type": file_type,
            "db_count": db_count,
            "actual_count": actual_count
        }
    )


def create_task_timeout_alert(ticket_id: str, timeout_minutes: int):
    """创建任务超时告警"""
    alert_system.create_alert(
        alert_type=AlertType.TASK_TIMEOUT,
        level=AlertLevel.CRITICAL,
        title="任务执行超时",
        message=f"任务 {ticket_id} 执行超过 {timeout_minutes} 分钟",
        ticket_id=ticket_id,
        metadata={
            "timeout_minutes": timeout_minutes
        }
    )


def get_alert_summary(hours: int = 24) -> dict:
    """获取告警摘要"""
    return alert_system.generate_alert_summary(hours)


def get_recent_alerts(hours: int = 24) -> list[dict]:
    """获取最近的告警"""
    alerts = alert_system.get_recent_alerts(hours)
    return [
        {
            "alert_id": alert.alert_id,
            "alert_type": alert.alert_type.value,
            "level": alert.level.value,
            "title": alert.title,
            "message": alert.message,
            "ticket_id": alert.ticket_id,
            "timestamp": alert.timestamp.isoformat(),
            "metadata": alert.metadata
        }
        for alert in alerts
    ]


# 示例使用
if __name__ == "__main__":
    # 创建一些示例告警
    create_message_processing_alert("test123", "UPDATE_STATUS", "数据库连接失败")
    create_data_integrity_alert("test456", "started_at_missing", "任务开始时间字段为空")
    create_selenium_session_missing_alert("test789")

    # 获取告警摘要
    summary = get_alert_summary()
    print(json.dumps(summary, indent=2, ensure_ascii=False))
