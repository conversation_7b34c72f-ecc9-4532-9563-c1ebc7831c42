services:
  webui-agent-executor:
    image: webui-agent-executor:1.0.0
    container_name: webui-agent-executor
    depends_on:
      - pgsql01
      - rabbitmq01
      - seleniumhub01
    volumes:
      - ./app:/app/app
      - ./keys:/app/keys
      - ./recordings:/app/recordings
    working_dir: /app
    environment:
      - TZ=Asia/Shanghai
      - DATABASE_URL=postgres://postgres:xxxx@localhost:5432/postgres
      - OPENAI_API_KEY=xxxx
      - OPENAI_BASE_URL=https://zhenze-huhehaote.cmecloud.cn/v1
      - OPENAI_MODEL=deepseek-v3-0324
      - OPENAI_TEMPERATURE=0
      - PLANNER_LLM_API_KEY=xxxx
      - PLANNER_LLM_BASE_URL=http://************:20008/v1
      - PLANNER_LLM_MODEL=Qwen3-32B
      - PLANNER_LLM_TEMPERATURE=0
      - ANONYMIZED_TELEMETRY=false
      # 覆盖settings.py中的配置
      - WEBUI_AGENT_URL=http://***********:8000/api/v1/webui
      - PORT=8080
      - SELENIUM_REMOTE_URL=http://seleniumhub01:4444
      - RABBITMQ_HOST=rabbitmq01
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=admin
      - RABBITMQ_PASSWORD=xxxx
      - ENABLE_TOKEN_TRUNCATION=false
    command: uv run main.py
    ports:
      - "5055:8080"
    networks:
      - webui-executor-network
  
  pgsql01:
    image: bitnami/postgresql:17.5.0
    container_name: pgsql01
    environment:
      - TZ=Asia/Shanghai
      - POSTGRESQL_PASSWORD=xxxx
      - POSTGRESQL_POSTGRES_PASSWORD=xxxx
    networks:
      - webui-executor-network
  
  rabbitmq01:
    image: rabbitmq:management
    container_name: rabbitmq01
    environment:
      - TZ=Asia/Shanghai
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=xxxx
    networks:
      - webui-executor-network

  seleniumhub01:
    image: selenium/hub:4.34.0
    container_name: seleniumhub01
    shm_size: 2gb
    environment:
      - TZ=Asia/Shanghai
      - SE_ENABLE_TRACING=false
    networks:
      - webui-executor-network

  seleniumnode01:
    image: selenium/node-chromium:4.34.0
    container_name: seleniumnode01
    depends_on:
      - seleniumhub01
    shm_size: 2gb
    environment:
      - TZ=Asia/Shanghai
      - SE_ENABLE_TRACING=false
      - SE_EVENT_BUS_HOST=seleniumhub01
      - SE_EVENT_BUS_PUBLISH_PORT=4442
      - SE_EVENT_BUS_SUBSCRIBE_PORT=4443
      - SE_NODE_DELETE_SESSION_ON_UI=true
      - SE_NODE_GRID_URL=http://seleniumhub01:4444
      - SE_NODE_PORT=5555
      - SE_START_VNC=false
      - SE_START_NO_VNC=false
      - SE_SCREEN_WIDTH=1920
      - SE_SCREEN_HEIGHT=1080
      - SE_SCREEN_DEPTH=24
      - SE_SCREEN_DPI=96
      - SE_BROWSER_ARGS_1="--window-size=1920,1080"
      - SE_BROWSER_ARGS_2="--incognito"
      - SE_BROWSER_ARGS_3="--no-sandbox"
      - SE_BROWSER_ARGS_4="--disable-dev-shm-usage"
      - SE_BROWSER_ARGS_5="--disable-gpu-sandbox"
      - SE_BROWSER_ARGS_6="--disable-setuid-sandbox"
      - SE_BROWSER_ARGS_7="--no-xshm"
      - SE_BROWSER_ARGS_8="--no-zygote"
      - SE_BROWSER_ARGS_9="--disable-site-isolation-trials"
      - SE_BROWSER_ARGS_10="--disable-web-security"
      - SE_BROWSER_ARGS_11="--disable-features=IsolateOrigins,site-per-process"
      - SE_BROWSER_ARGS_12="--allow-running-insecure-content"
      - SE_BROWSER_ARGS_13="--ignore-ssl-errors"
      - SE_BROWSER_ARGS_14="--ignore-certificate-errors-spki-list"
      - SE_BROWSER_ARGS_15="--ignore-certificate-errors"
    networks:
      - webui-executor-network

networks:
  webui-executor-network:
    driver: bridge