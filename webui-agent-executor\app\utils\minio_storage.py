"""
MinIO对象存储服务

提供统一的文件存储接口，支持上传、下载、删除等操作
"""

import asyncio
from datetime import timedelta
from pathlib import Path

from minio import Minio
from minio.error import S3Error

from app.config.settings import settings
from app.utils.log import logger


class MinIOStorage:
    """MinIO对象存储服务类"""

    def __init__(self):
        """初始化MinIO客户端"""
        self.client = None
        self.bucket_name = settings.minio_bucket_name
        self._initialize_client()

    def _initialize_client(self):
        """初始化MinIO客户端连接"""
        try:
            self.client = Minio(
                endpoint=settings.minio_endpoint,
                access_key=settings.minio_access_key,
                secret_key=settings.minio_secret_key,
                secure=settings.minio_secure,
                region=settings.minio_region,
            )

            # 检查存储桶是否存在，不存在则创建
            self._ensure_bucket_exists()
            logger.info(f"MinIO客户端初始化成功，连接到: {settings.minio_endpoint}")

        except Exception as e:
            logger.error(f"MinIO客户端初始化失败: {e}")
            raise

    def _ensure_bucket_exists(self):
        """确保存储桶存在，不存在则创建"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name, location=settings.minio_region)
                logger.info(f"创建MinIO存储桶: {self.bucket_name}")
            else:
                logger.info(f"MinIO存储桶已存在: {self.bucket_name}")
        except S3Error as e:
            logger.error(f"检查或创建存储桶失败: {e}")
            raise

    async def upload_file(
        self,
        file_path: str | Path,
        object_name: str,
        content_type: str | None = None,
    ) -> str:
        """
        上传文件到MinIO

        Args:
            file_path: 本地文件路径
            object_name: 对象存储中的文件名（包含路径）
            content_type: 文件MIME类型

        Returns:
            str: 上传成功后的对象路径

        Raises:
            Exception: 上传失败时抛出异常
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 如果没有指定content_type，尝试根据文件扩展名推断
            if content_type is None:
                content_type = self._get_content_type(file_path)

            # 在异步环境中运行同步的MinIO操作
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.fput_object(
                    bucket_name=self.bucket_name,
                    object_name=object_name,
                    file_path=str(file_path),
                    content_type=content_type,
                ),
            )

            logger.info(f"文件上传成功: {file_path} -> {object_name}")
            return object_name

        except Exception as e:
            logger.error(f"文件上传失败: {file_path} -> {object_name}, 错误: {e}")
            raise

    async def upload_data(
        self,
        data: bytes,
        object_name: str,
        content_type: str | None = None,
    ) -> str:
        """
        上传二进制数据到MinIO

        Args:
            data: 二进制数据
            object_name: 对象存储中的文件名（包含路径）
            content_type: 文件MIME类型

        Returns:
            str: 上传成功后的对象路径

        Raises:
            Exception: 上传失败时抛出异常
        """
        try:
            from io import BytesIO

            data_stream = BytesIO(data)
            data_length = len(data)

            # 在异步环境中运行同步的MinIO操作
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.put_object(
                    bucket_name=self.bucket_name,
                    object_name=object_name,
                    data=data_stream,
                    length=data_length,
                    content_type=content_type,
                ),
            )

            logger.info(f"数据上传成功: {object_name} ({data_length} bytes)")
            return object_name

        except Exception as e:
            logger.error(f"数据上传失败: {object_name}, 错误: {e}")
            raise

    async def download_file(self, object_name: str, file_path: str | Path) -> bool:
        """
        从MinIO下载文件到本地

        Args:
            object_name: 对象存储中的文件名
            file_path: 本地保存路径

        Returns:
            bool: 下载是否成功

        Raises:
            Exception: 下载失败时抛出异常
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 在异步环境中运行同步的MinIO操作
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.fget_object(
                    bucket_name=self.bucket_name,
                    object_name=object_name,
                    file_path=str(file_path),
                ),
            )

            logger.info(f"文件下载成功: {object_name} -> {file_path}")
            return True

        except Exception as e:
            logger.error(f"文件下载失败: {object_name} -> {file_path}, 错误: {e}")
            raise

    async def download_data(self, object_name: str) -> bytes:
        """
        从MinIO下载文件数据

        Args:
            object_name: 对象存储中的文件名

        Returns:
            bytes: 文件二进制数据

        Raises:
            Exception: 下载失败时抛出异常
        """
        try:
            # 在异步环境中运行同步的MinIO操作
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.get_object(
                    bucket_name=self.bucket_name,
                    object_name=object_name,
                ),
            )

            data = response.read()
            response.close()
            response.release_conn()

            logger.info(f"数据下载成功: {object_name} ({len(data)} bytes)")
            return data

        except Exception as e:
            logger.error(f"数据下载失败: {object_name}, 错误: {e}")
            raise

    async def delete_file(self, object_name: str) -> bool:
        """
        删除MinIO中的文件

        Args:
            object_name: 对象存储中的文件名

        Returns:
            bool: 删除是否成功

        Raises:
            Exception: 删除失败时抛出异常
        """
        try:
            # 在异步环境中运行同步的MinIO操作
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.remove_object(
                    bucket_name=self.bucket_name,
                    object_name=object_name,
                ),
            )

            logger.info(f"文件删除成功: {object_name}")
            return True

        except Exception as e:
            logger.error(f"文件删除失败: {object_name}, 错误: {e}")
            raise

    def get_file_url(self, object_name: str, expires: timedelta | None = None) -> str:
        """
        获取文件的预签名URL

        Args:
            object_name: 对象存储中的文件名
            expires: URL过期时间，默认使用配置中的值

        Returns:
            str: 预签名URL

        Raises:
            Exception: 获取URL失败时抛出异常
        """
        try:
            if expires is None:
                expires = timedelta(seconds=settings.minio_url_expiry)

            url = self.client.presigned_get_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                expires=expires,
            )

            logger.debug(f"生成文件URL: {object_name} -> {url}")
            return url

        except Exception as e:
            logger.error(f"获取文件URL失败: {object_name}, 错误: {e}")
            raise

    def file_exists(self, object_name: str) -> bool:
        """
        检查文件是否存在

        Args:
            object_name: 对象存储中的文件名

        Returns:
            bool: 文件是否存在
        """
        try:
            self.client.stat_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
            )
            return True
        except S3Error as e:
            if e.code == "NoSuchKey":
                return False
            logger.error(f"检查文件存在性失败: {object_name}, 错误: {e}")
            raise
        except Exception as e:
            logger.error(f"检查文件存在性失败: {object_name}, 错误: {e}")
            raise

    def _get_content_type(self, file_path: Path) -> str:
        """
        根据文件扩展名推断MIME类型

        Args:
            file_path: 文件路径

        Returns:
            str: MIME类型
        """
        import mimetypes

        content_type, _ = mimetypes.guess_type(str(file_path))
        return content_type or "application/octet-stream"

    def generate_object_name(self, ticket_id: str, file_type: str, filename: str) -> str:
        """
        生成对象存储中的文件名

        Args:
            ticket_id: 任务ID
            file_type: 文件类型（video, screenshot, file）
            filename: 原始文件名

        Returns:
            str: 对象存储中的文件名
        """
        return f"{file_type}/{ticket_id}/{filename}"


# 全局MinIO存储实例
_minio_storage: MinIOStorage | None = None


def get_minio_storage() -> MinIOStorage:
    """
    获取MinIO存储实例（单例模式）

    Returns:
        MinIOStorage: MinIO存储实例
    """
    global _minio_storage
    if _minio_storage is None:
        _minio_storage = MinIOStorage()
    return _minio_storage


async def upload_file_to_minio(
    file_path: str | Path,
    ticket_id: str,
    file_type: str,
    filename: str | None = None,
) -> str:
    """
    上传文件到MinIO的便捷函数

    Args:
        file_path: 本地文件路径
        ticket_id: 任务ID
        file_type: 文件类型（video, screenshot, file）
        filename: 自定义文件名，如果不提供则使用原文件名

    Returns:
        str: 对象存储中的文件路径
    """
    if not settings.minio_enabled:
        logger.warning("MinIO存储未启用，跳过文件上传")
        return str(file_path)

    storage = get_minio_storage()
    file_path = Path(file_path)

    if filename is None:
        filename = file_path.name

    object_name = storage.generate_object_name(ticket_id, file_type, filename)

    try:
        await storage.upload_file(file_path, object_name)
        return object_name
    except Exception as e:
        logger.error(f"上传文件到MinIO失败: {e}")
        # 如果上传失败，返回本地路径作为备选
        return str(file_path)
