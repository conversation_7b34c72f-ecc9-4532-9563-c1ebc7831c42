#!/usr/bin/env python3
"""
基于browser-use标准的DeepSeek集成
使用browser-use的消息系统和ChatInvokeCompletion
"""

import json
import re
from dataclasses import dataclass
from typing import Any, TypeVar, overload

import tiktoken
from browser_use.llm.base import BaseChatModel
from browser_use.llm.exceptions import ModelProviderError, ModelRateLimitError
from browser_use.llm.messages import (
    AssistantMessage,
    BaseMessage,
    SystemMessage,
    UserMessage,
)
from browser_use.llm.views import ChatInvokeCompletion, ChatInvokeUsage
from openai import (
    APIConnectionError,
    APIError,
    APIStatusError,
    APITimeoutError,
    AsyncOpenAI,
    RateLimitError,
)

from app.config.settings import settings
from app.utils.log import logger

T = TypeVar("T", bound=Any)


@dataclass
class BrowserUseDeepSeek(BaseChatModel):
    """
    基于browser-use标准的DeepSeek集成
    正确处理DeepSeek模型的输出格式，返回ChatInvokeCompletion对象
    包含token管理和内容截断功能
    """

    model: str = "deepseek-v3-0324"
    api_key: str | None = None
    base_url: str | None = None
    max_tokens: int | None = 16384
    temperature: float | None = 0.0
    # Token管理配置（从settings读取，可以通过参数覆盖）
    enable_token_truncation: bool | None = None
    max_input_tokens: int | None = None
    token_buffer: int | None = None

    def __post_init__(self):
        """初始化内部的OpenAI客户端和tokenizer"""
        self._client = AsyncOpenAI(api_key=self.api_key, base_url=self.base_url)

        # 从settings读取token管理配置（如果未在初始化时指定）
        self.enable_token_truncation = (
            self.enable_token_truncation
            if self.enable_token_truncation is not None
            else settings.enable_token_truncation
        )
        self.max_input_tokens = (
            self.max_input_tokens if self.max_input_tokens is not None else settings.max_input_tokens
        )
        self.token_buffer = self.token_buffer if self.token_buffer is not None else settings.token_buffer

        # 初始化tokenizer用于token计数（仅在启用截断时）
        if self.enable_token_truncation:
            try:
                self._tokenizer = tiktoken.encoding_for_model("gpt-4")  # 使用通用tokenizer
            except Exception:
                self._tokenizer = tiktoken.get_encoding("cl100k_base")  # 回退到基础编码
        else:
            self._tokenizer = None

        logger.info(
            f"BrowserUseDeepSeek初始化: model={self.model}, token_truncation={self.enable_token_truncation}, max_input_tokens={self.max_input_tokens}"
        )

    @property
    def provider(self) -> str:
        return "deepseek"

    @property
    def name(self) -> str:
        return self.model

    @overload
    async def ainvoke(
        self,
        messages: list[BaseMessage],
        output_format: None = None,
        tools: list[dict[str, Any]] | None = None,
        stop: list[str] | None = None,
    ) -> ChatInvokeCompletion[str]: ...

    @overload
    async def ainvoke(
        self,
        messages: list[BaseMessage],
        output_format: type[T],
        tools: list[dict[str, Any]] | None = None,
        stop: list[str] | None = None,
    ) -> ChatInvokeCompletion[T]: ...

    async def ainvoke(
        self,
        messages: list[BaseMessage],
        output_format: type[T] | None = None,
        tools: list[dict[str, Any]] | None = None,
        stop: list[str] | None = None,
    ) -> ChatInvokeCompletion[T] | ChatInvokeCompletion[str]:
        """
        实现browser-use标准的ainvoke方法，包含token管理
        """
        try:
            logger.debug(f"ainvoke调用: messages={len(messages)}, output_format={output_format}")

            # 转换browser-use消息为OpenAI格式
            openai_messages = self._convert_messages(messages)

            # 检查并截断消息以避免token限制（如果启用了截断功能）
            if self.enable_token_truncation:
                openai_messages = self._truncate_messages_if_needed(openai_messages)
            else:
                logger.debug("Token截断功能已禁用，跳过消息截断")

            # 调用OpenAI API
            response = await self._client.chat.completions.create(
                model=self.model, messages=openai_messages, max_tokens=self.max_tokens, temperature=self.temperature
            )

            content = response.choices[0].message.content or ""

            # 提取真实的usage信息
            usage_info = response.usage if hasattr(response, "usage") and response.usage else None

            # 处理响应
            if output_format is not None:
                # 结构化输出
                cleaned_content = self._clean_json_response(content)
                try:
                    parsed_data = json.loads(cleaned_content)
                    validated_data = output_format.model_validate(parsed_data)
                    return ChatInvokeCompletion(completion=validated_data, usage=self._create_usage(usage_info))
                except json.JSONDecodeError as json_error:
                    logger.error(f"结构化输出解析失败: {json_error}")
                    logger.debug(f"原始响应内容: {content}")
                    logger.debug(f"清理后内容: {cleaned_content}")
                    logger.debug(f"JSON错误位置: line {json_error.lineno}, column {json_error.colno}")

                    # 显示错误附近的内容
                    self._log_json_error_context(cleaned_content, json_error)

                    # 尝试更激进的修复
                    try:
                        fallback_content = self._create_fallback_response(cleaned_content, output_format)
                        if fallback_content:
                            validated_data = output_format.model_validate(fallback_content)
                            logger.warning("使用回退响应成功解析")
                            return ChatInvokeCompletion(completion=validated_data, usage=self._create_usage(usage_info))
                    except Exception as fallback_error:
                        logger.error(f"回退解析也失败: {fallback_error}")

                    raise ModelProviderError(
                        f"Failed to parse structured output: {json_error}", model=self.name
                    ) from json_error
                except Exception as e:
                    logger.error(f"结构化输出验证失败: {e}")
                    logger.debug(f"原始响应内容: {content}")
                    logger.debug(f"清理后内容: {cleaned_content}")

                    # 尝试更激进的修复
                    try:
                        fallback_content = self._create_fallback_response(cleaned_content, output_format)
                        if fallback_content:
                            validated_data = output_format.model_validate(fallback_content)
                            logger.warning("使用回退响应成功解析")
                            return ChatInvokeCompletion(completion=validated_data, usage=self._create_usage(usage_info))
                    except Exception as fallback_error:
                        logger.error(f"回退解析也失败: {fallback_error}")

                    raise ModelProviderError(f"Failed to parse structured output: {e}", model=self.name) from e
            else:
                # 文本输出
                cleaned_content = self._clean_json_response(content)
                return ChatInvokeCompletion(completion=cleaned_content, usage=self._create_usage(usage_info))

        except RateLimitError as e:
            raise ModelRateLimitError(str(e), model=self.name) from e
        except (APIError, APIConnectionError, APITimeoutError, APIStatusError) as e:
            raise ModelProviderError(str(e), model=self.name) from e
        except Exception as e:
            logger.error(f"ainvoke执行失败: {e}")
            raise ModelProviderError(str(e), model=self.name) from e

    def _convert_messages(self, messages: list[BaseMessage]) -> list:
        """将browser-use消息转换为OpenAI格式"""
        openai_messages = []
        for msg in messages:
            if isinstance(msg, UserMessage):
                # 兼容新旧版本的browser-use消息格式
                content = getattr(msg, "content", None) or getattr(msg, "text", "")
                openai_messages.append({"role": "user", "content": content})
            elif isinstance(msg, SystemMessage):
                content = getattr(msg, "content", None) or getattr(msg, "text", "")
                openai_messages.append({"role": "system", "content": content})
            elif isinstance(msg, AssistantMessage):
                content = getattr(msg, "content", None) or getattr(msg, "text", "")
                openai_messages.append({"role": "assistant", "content": content})
            else:
                logger.warning(f"未知消息类型: {type(msg)}")

        return openai_messages

    def _clean_json_response(self, response_text: str) -> str:
        """清理DeepSeek模型的响应格式"""
        if not isinstance(response_text, str):
            logger.warning(f"响应不是字符串: {type(response_text)}")
            return str(response_text)

        # 记录原始响应用于调试
        logger.debug(f"原始响应长度: {len(response_text)}")

        # 移除markdown代码块
        cleaned = self._remove_markdown_blocks(response_text.strip())

        # 移除thinking字段
        cleaned = self._remove_thinking_fields(cleaned)

        # 修复常见JSON问题
        cleaned = self._fix_common_json_issues(cleaned)

        # 验证JSON格式
        cleaned = self._validate_and_fix_json(cleaned)

        return cleaned

    def _fix_response_format(self, text: str) -> str:
        """公共的响应格式修复方法，供测试使用"""
        return self._fix_common_json_issues(text)

    def _remove_markdown_blocks(self, text: str) -> str:
        """移除markdown代码块"""
        # 简单但可靠的方法
        if "```json" in text and "```" in text:
            start_idx = text.find("```json")
            if start_idx != -1:
                content_start = start_idx + len("```json")
                end_idx = text.find("```", content_start)
                if end_idx != -1:
                    return text[content_start:end_idx].strip()

        if text.count("```") >= 2:
            parts = text.split("```")
            if len(parts) >= 3:
                return parts[1].strip()

        # 尝试提取JSON对象
        # 查找第一个{和最后一个}
        first_brace = text.find("{")
        if first_brace != -1:
            # 从第一个{开始查找匹配的}
            brace_count = 0
            for i in range(first_brace, len(text)):
                if text[i] == "{":
                    brace_count += 1
                elif text[i] == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        # 找到匹配的}，提取JSON
                        return text[first_brace : i + 1]

        return text

    def _remove_thinking_fields(self, text: str) -> str:
        """移除thinking字段并修复scroll格式"""
        # 移除thinking字段 - 处理多行内容
        text = re.sub(r'"thinking"\s*:\s*"[^"]*(?:\\.[^"]*)*"', "", text, flags=re.DOTALL)

        # 修复scroll格式 - 转换为browser-use期望的格式
        # ScrollAction格式：{"down": bool, "num_pages": float}

        # 将 {"scroll": {"down": 数字}} 转换为 {"scroll": {"down": true, "num_pages": 数字}}
        def fix_scroll_down_format(match):
            num_pages = match.group(1)
            return f'"scroll": {{"down": true, "num_pages": {num_pages}}}'

        text = re.sub(r'"scroll"\s*:\s*{\s*"down"\s*:\s*(\d+(?:\.\d+)?)\s*}', fix_scroll_down_format, text)

        # 将 {"scroll": {"up": 数字}} 转换为 {"scroll": {"down": false, "num_pages": 数字}}
        def fix_scroll_up_format(match):
            num_pages = match.group(1)
            return f'"scroll": {{"down": false, "num_pages": {num_pages}}}'

        text = re.sub(r'"scroll"\s*:\s*{\s*"up"\s*:\s*(\d+(?:\.\d+)?)\s*}', fix_scroll_up_format, text)

        # 修复错误的up: true格式为down: false
        text = re.sub(
            r'"scroll"\s*:\s*{\s*"up"\s*:\s*true\s*,\s*"num_pages"\s*:\s*(\d+(?:\.\d+)?)\s*}',
            lambda m: f'"scroll": {{"down": false, "num_pages": {m.group(1)}}}',
            text,
        )

        # 处理旧的num_pages格式（默认向下滚动）
        text = re.sub(
            r'"scroll"\s*:\s*{\s*"num_pages"\s*:\s*(\d+(?:\.\d+)?)\s*}',
            lambda m: f'"scroll": {{"down": true, "num_pages": {m.group(1)}}}',
            text,
        )

        # 修复extract_structured_data格式
        # 情况1: 将 {"extract_structured_data": {"instruction": "..."}}
        # 转换为 {"extract_structured_data": {"query": "...", "extract_links": true}}
        def fix_extract_data_format_instruction(match):
            instruction = match.group(1)
            return f'"extract_structured_data": {{"query": "{instruction}", "extract_links": true}}'

        text = re.sub(
            r'"extract_structured_data"\s*:\s*{\s*"instruction"\s*:\s*"([^"]+)"\s*}',
            fix_extract_data_format_instruction,
            text,
        )

        # 情况2: 将 {"extract_structured_data": {"query": "..."}}
        # 转换为 {"extract_structured_data": {"query": "...", "extract_links": true}}
        def fix_extract_data_format_query(match):
            query = match.group(1)
            return f'"extract_structured_data": {{"query": "{query}", "extract_links": true}}'

        text = re.sub(
            r'"extract_structured_data"\s*:\s*{\s*"query"\s*:\s*"([^"]+)"\s*}', fix_extract_data_format_query, text
        )

        # 情况3: 处理更复杂的extract_structured_data格式，确保包含extract_links
        # 如果已经有query但没有extract_links，添加extract_links
        def add_extract_links(match):
            logger.debug(f"匹配到extract_structured_data: {match.group(0)}")
            full_content = match.group(1)  # 这是 {"query": "..."}
            logger.debug(f"提取的内容: {full_content}")

            if '"extract_links"' not in full_content:
                logger.debug("需要添加extract_links")
                # 移除最后的}，添加extract_links，然后重新添加}
                content_without_brace = full_content.rstrip().rstrip("}").strip()
                if content_without_brace.endswith(","):
                    new_content = f'{content_without_brace} "extract_links": true}}'
                else:
                    new_content = f'{content_without_brace}, "extract_links": true}}'

                result = f'"extract_structured_data": {new_content}'
                logger.debug(f"修复结果: {result}")
                return result
            return match.group(0)  # 如果已经有extract_links，不修改

        # 使用更宽松的匹配，支持嵌套结构和中文字符
        logger.debug(f"开始处理extract_structured_data格式修复，输入: {text}")
        # 使用DOTALL标志支持多行匹配，使用更宽松的字符匹配
        text = re.sub(r'"extract_structured_data"\s*:\s*(\{[^}]+\})', add_extract_links, text, flags=re.DOTALL)
        logger.debug(f"extract_structured_data格式修复后: {text}")

        # 修复navigate格式 - DeepSeek有时返回错误的navigate格式
        # 将 {"navigate": {"url": "..."}} 转换为 {"go_to_url": {"url": "..."}}
        text = re.sub(
            r'"navigate"\s*:\s*{\s*"url"\s*:\s*"([^"]+)"\s*}', lambda m: f'"go_to_url": {{"url": "{m.group(1)}"}}', text
        )

        # 修复其他可能的错误格式
        # 将 {"navigate": {"url": "...", "new_tab": true}} 转换为 {"go_to_url": {"url": "...", "new_tab": true}}
        text = re.sub(
            r'"navigate"\s*:\s*{\s*"url"\s*:\s*"([^"]+)"\s*,\s*"new_tab"\s*:\s*(true|false)\s*}',
            lambda m: f'"go_to_url": {{"url": "{m.group(1)}", "new_tab": {m.group(2)}}}',
            text,
        )

        # 修复switch_tab和close_tab格式 - 将index转换为page_id
        def fix_tab_action_format(match):
            action_name = match.group(1)  # switch_tab 或 close_tab
            page_id = match.group(2)  # 数字
            return f'"{action_name}": {{"page_id": {page_id}}}'

        text = re.sub(r'"(switch_tab|close_tab)"\s*:\s*{\s*"index"\s*:\s*(\d+)\s*}', fix_tab_action_format, text)

        # 清理多余的逗号和空白
        text = re.sub(r",\s*,", ",", text)  # 连续逗号
        text = re.sub(r",\s*}", "}", text)  # 结尾逗号
        text = re.sub(r",\s*]", "]", text)  # 数组结尾逗号
        text = re.sub(r"{\s*,", "{", text)  # 开头逗号

        return text

    def _fix_common_json_issues(self, text: str) -> str:
        """修复常见的JSON格式问题"""
        # 首先应用所有格式修复
        text = self._remove_thinking_fields(text)

        # 然后确保JSON格式正确
        text = text.strip()
        if not text.startswith("{"):
            text = "{" + text
        if not text.endswith("}"):
            text = text + "}"

        return text

    def _validate_and_fix_json(self, text: str) -> str:
        """验证并修复JSON格式"""
        try:
            # 尝试解析JSON以验证格式
            json.loads(text)
            return text
        except json.JSONDecodeError as e:
            logger.debug(f"JSON格式错误: {e}, 尝试修复...")

            # 尝试各种修复策略
            fixed_text = self._apply_json_fixes(text, e)

            # 再次验证修复后的JSON
            try:
                json.loads(fixed_text)
                logger.debug("JSON修复成功")
                return fixed_text
            except json.JSONDecodeError:
                logger.warning("JSON修复失败，返回原始文本")
                return text

    def _apply_json_fixes(self, text: str, error: json.JSONDecodeError) -> str:
        """应用各种JSON修复策略"""
        error_msg = str(error)
        original_text = text

        # 修复策略0: 处理开头的非JSON字符
        text = text.strip()
        if text and not text.startswith("{"):
            # 查找第一个 { 字符
            first_brace = text.find("{")
            if first_brace > 0:
                text = text[first_brace:]
                logger.debug("移除了JSON开头的非法字符")

        # 修复策略1: 处理属性名缺少引号的问题
        if "Expecting property name enclosed in double quotes" in error_msg:
            # 更精确的属性名引号修复
            text = re.sub(r"(?<=[{,]\s*)(\w+)(?=\s*:)", r'"\1"', text)
            logger.debug("应用了属性名引号修复")

        # 修复策略2: 处理字符串值缺少引号的问题
        if "Expecting value" in error_msg:
            # 修复未加引号的字符串值（避免影响数字和布尔值）
            text = re.sub(r":\s*([a-zA-Z_][a-zA-Z0-9_\s]*?)(?=\s*[,}])", r': "\1"', text)
            logger.debug("应用了字符串值引号修复")

        # 修复策略3: 处理尾随逗号
        text = re.sub(r",\s*}", "}", text)  # 对象结尾逗号
        text = re.sub(r",\s*]", "]", text)  # 数组结尾逗号

        # 修复策略4: 处理截断的JSON
        if not text.endswith("}") and text.count("{") > text.count("}"):
            # 补充缺失的右括号
            missing_braces = text.count("{") - text.count("}")
            text += "}" * missing_braces
            logger.debug(f"补充了 {missing_braces} 个右括号")

        # 修复策略5: 处理单引号
        text = text.replace("'", '"')

        # 修复策略6: 处理多余的反斜杠
        text = re.sub(r'\\(?!["\\/bfnrt])', "", text)

        # 修复策略7: 处理换行符和特殊字符
        text = text.replace("\n", "\\n").replace("\r", "\\r").replace("\t", "\\t")

        if text != original_text:
            logger.debug(f"JSON修复前: {original_text[:100]}...")
            logger.debug(f"JSON修复后: {text[:100]}...")

        return text

    def _log_json_error_context(self, content: str, error: json.JSONDecodeError) -> None:
        """记录JSON错误的上下文信息"""
        try:
            lines = content.split("\n")
            error_line = error.lineno - 1  # 转换为0基索引

            # 显示错误行及其前后几行
            start_line = max(0, error_line - 2)
            end_line = min(len(lines), error_line + 3)

            logger.debug("JSON错误上下文:")
            for i in range(start_line, end_line):
                if i < len(lines):
                    marker = " >>> " if i == error_line else "     "
                    logger.debug(f"{marker}Line {i + 1}: {lines[i]}")

            # 显示错误列位置
            if error_line < len(lines) and error.colno > 0:
                error_pointer = " " * (error.colno - 1) + "^"
                logger.debug(f"     Error position: {error_pointer}")

        except Exception as e:
            logger.debug(f"无法显示JSON错误上下文: {e}")

    def _create_usage(self, usage_info=None) -> ChatInvokeUsage:
        """创建usage对象，使用真实的API返回数据"""
        if usage_info:
            return ChatInvokeUsage(
                prompt_tokens=getattr(usage_info, "prompt_tokens", 0),
                completion_tokens=getattr(usage_info, "completion_tokens", 0),
                total_tokens=getattr(usage_info, "total_tokens", 0),
                prompt_cached_tokens=getattr(usage_info, "prompt_cached_tokens", 0),
                prompt_cache_creation_tokens=getattr(usage_info, "prompt_cache_creation_tokens", 0),
                prompt_image_tokens=getattr(usage_info, "prompt_image_tokens", 0),
            )
        else:
            # 如果没有usage信息，返回默认值
            return ChatInvokeUsage(
                prompt_tokens=0,
                completion_tokens=0,
                total_tokens=0,
                prompt_cached_tokens=0,
                prompt_cache_creation_tokens=0,
                prompt_image_tokens=0,
            )

    def _create_default_response(self) -> str:
        """创建默认响应"""
        return json.dumps(
            {
                "evaluation_previous_goal": "开始执行浏览器自动化任务",
                "memory": "初始化浏览器会话",
                "next_goal": "访问百度首页开始搜索",
                "action": [{"go_to_url": {"url": "https://www.baidu.com"}}],
            },
            ensure_ascii=False,
        )

    def _count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        if not self.enable_token_truncation or not self._tokenizer:
            # 如果未启用截断功能，返回简单估算
            return len(text) // 4

        try:
            return len(self._tokenizer.encode(text))
        except Exception as e:
            logger.warning(f"Token计数失败: {e}")
            # 简单估算：1 token ≈ 4 字符
            return len(text) // 4

    def _count_messages_tokens(self, messages: list[dict]) -> int:
        """计算消息列表的总token数量"""
        total_tokens = 0
        for message in messages:
            content = message.get("content", "")
            role = message.get("role", "")
            # 计算内容token + 角色token + 格式开销
            total_tokens += self._count_tokens(content)
            total_tokens += self._count_tokens(role)
            total_tokens += 4  # 消息格式开销
        return total_tokens

    def _truncate_messages_if_needed(self, messages: list[dict]) -> list[dict]:
        """如果消息超过token限制，进行智能截断"""
        if not self.enable_token_truncation:
            logger.debug("Token截断功能已禁用，直接返回原始消息")
            return messages

        total_tokens = self._count_messages_tokens(messages)

        max_tokens = self.max_input_tokens or 8192  # 提供默认值
        if total_tokens <= max_tokens:
            logger.debug(f"Token数量正常: {total_tokens}/{max_tokens}")
            return messages

        logger.warning(f"Token数量超限: {total_tokens}/{max_tokens}，开始智能截断")

        # 保留系统消息和最后几条消息
        truncated_messages = []
        remaining_tokens = max_tokens - (self.token_buffer or 576)  # 预留缓冲区

        # 1. 首先保留系统消息（系统消息通常很重要）
        system_messages = [msg for msg in messages if msg.get("role") == "system"]
        for msg in system_messages:
            msg_tokens = self._count_tokens(msg.get("content", "")) + 10
            if msg_tokens <= remaining_tokens:
                truncated_messages.append(msg)
                remaining_tokens -= msg_tokens
            else:
                # 如果系统消息太长，尝试截断
                max_content_tokens = remaining_tokens - 50
                if max_content_tokens > 100:
                    truncated_content = self._truncate_content(msg.get("content", ""), max_content_tokens)
                    if truncated_content:
                        truncated_msg = msg.copy()
                        truncated_msg["content"] = truncated_content
                        truncated_messages.append(truncated_msg)
                        remaining_tokens -= self._count_tokens(truncated_content) + 10

        # 2. 从后往前保留用户和助手消息（保持对话上下文）
        non_system_messages = [msg for msg in messages if msg.get("role") != "system"]

        # 优先保留最后的几轮对话
        preserved_messages = []
        for msg in reversed(non_system_messages):
            content = msg.get("content", "")
            msg_tokens = self._count_tokens(content) + 10

            if msg_tokens <= remaining_tokens:
                preserved_messages.insert(0, msg)  # 保持原始顺序
                remaining_tokens -= msg_tokens
            else:
                # 如果单条消息太长，尝试智能截断
                if remaining_tokens > 200:  # 确保有足够空间
                    max_content_tokens = remaining_tokens - 100  # 保守的预留
                    truncated_content = self._truncate_content(content, max_content_tokens)
                    if truncated_content and len(truncated_content) > 50:  # 确保截断后仍有意义
                        truncated_msg = msg.copy()
                        truncated_msg["content"] = truncated_content
                        preserved_messages.insert(0, truncated_msg)
                        remaining_tokens -= self._count_tokens(truncated_content) + 10
                break

        # 合并系统消息和保留的对话消息
        truncated_messages.extend(preserved_messages)

        final_tokens = self._count_messages_tokens(truncated_messages)
        logger.info(
            f"消息截断完成: {len(messages)} -> {len(truncated_messages)} 条消息, {total_tokens} -> {final_tokens} tokens"
        )

        return truncated_messages

    def _truncate_content(self, content: str, max_tokens: int) -> str:
        """智能截断单条消息内容"""
        if self._count_tokens(content) <= max_tokens:
            return content

        # 简单的二分查找截断
        left, right = 0, len(content)
        best_content = ""

        while left <= right:
            mid = (left + right) // 2
            candidate = content[:mid]

            if self._count_tokens(candidate) <= max_tokens:
                best_content = candidate
                left = mid + 1
            else:
                right = mid - 1

        # 尝试在合适的位置截断（句号、换行等）
        if best_content and len(best_content) > 100:
            # 查找最后的句号或换行
            for delimiter in ["。", ".", "\n", "；", ";", "，", ","]:
                last_pos = best_content.rfind(delimiter)
                if last_pos > len(best_content) * 0.7:  # 确保不会截断太多
                    candidate = best_content[: last_pos + 1]
                    if self._count_tokens(candidate) <= max_tokens:
                        best_content = candidate
                        break

        # 如果截断后的内容太短，至少保留一些有意义的内容
        if len(best_content) < 50 and max_tokens > 20:
            # 尝试保留前面的重要部分
            words = content.split()[:20]  # 前20个词
            candidate = " ".join(words) + "..."
            if self._count_tokens(candidate) <= max_tokens:
                best_content = candidate

        return best_content if best_content else content[: max_tokens * 2]  # 最后的回退

    def _create_fallback_response(self, failed_content: str, output_format: type) -> dict | None:
        """创建回退响应，当原始解析失败时使用"""
        try:
            logger.info("尝试创建回退响应...")

            # 检查是否是AgentOutput类型的错误
            if hasattr(output_format, "__name__") and "AgentOutput" in str(output_format):
                # 尝试从失败的内容中提取有用信息
                logger.debug(f"分析失败内容: {failed_content[:200]}...")

                # 策略1: 提取extract_structured_data操作
                if "extract_structured_data" in failed_content:
                    query_match = re.search(r'"query"\s*:\s*"([^"]+)"', failed_content)
                    if query_match:
                        query = query_match.group(1)
                        logger.info(f"从失败内容中提取到query: {query}")

                        fallback_response = {
                            "evaluation_previous_goal": "尝试提取页面数据",
                            "memory": "正在处理数据提取任务",
                            "next_goal": "完成数据提取操作",
                            "action": [{"extract_structured_data": {"query": query, "extract_links": True}}],
                        }
                        logger.info("创建了extract_structured_data回退响应")
                        return fallback_response

                # 策略2: 提取click操作
                if "click" in failed_content:
                    selector_match = re.search(r'"selector"\s*:\s*"([^"]+)"', failed_content)
                    if selector_match:
                        selector = selector_match.group(1)
                        logger.info(f"从失败内容中提取到selector: {selector}")

                        fallback_response = {
                            "evaluation_previous_goal": "尝试点击页面元素",
                            "memory": "正在处理点击操作",
                            "next_goal": "完成点击操作",
                            "action": [{"click": {"selector": selector}}],
                        }
                        logger.info("创建了click回退响应")
                        return fallback_response

                # 策略3: 提取input_text操作
                if "input_text" in failed_content:
                    selector_match = re.search(r'"selector"\s*:\s*"([^"]+)"', failed_content)
                    text_match = re.search(r'"text"\s*:\s*"([^"]+)"', failed_content)
                    if selector_match and text_match:
                        selector = selector_match.group(1)
                        text = text_match.group(1)
                        logger.info(f"从失败内容中提取到input操作: {selector} -> {text}")

                        fallback_response = {
                            "evaluation_previous_goal": "尝试输入文本",
                            "memory": "正在处理文本输入操作",
                            "next_goal": "完成文本输入",
                            "action": [{"input_text": {"selector": selector, "text": text}}],
                        }
                        logger.info("创建了input_text回退响应")
                        return fallback_response

                # 策略4: 通用done响应
                fallback_response = {
                    "evaluation_previous_goal": "遇到解析错误，尝试完成任务",
                    "memory": "由于响应格式问题，使用回退机制",
                    "next_goal": "完成当前任务",
                    "action": [{"done": {"text": "由于响应格式问题，任务可能未完全完成", "success": False}}],
                }
                logger.info("创建了done回退响应")
                return fallback_response

            return None

        except Exception as e:
            logger.error(f"创建回退响应失败: {e}")
            return None
