"""
时区工具函数

提供统一的时区处理功能
"""

from datetime import datetime, timedelta, timezone

# 设置上海时区
SHANGHAI_TZ = timezone(timedelta(hours=8))


def get_shanghai_now() -> datetime:
    """获取上海时区的当前时间"""
    return datetime.now(SHANGHAI_TZ)


def to_shanghai_tz(dt: datetime) -> datetime:
    """将时间转换为上海时区

    Args:
        dt: 要转换的时间对象

    Returns:
        转换后的上海时区时间
    """
    if dt.tzinfo is None:
        # 如果没有时区信息，假设是上海时区
        return dt.replace(tzinfo=SHANGHAI_TZ)
    else:
        # 如果有时区信息，转换到上海时区
        return dt.astimezone(SHANGHAI_TZ)
