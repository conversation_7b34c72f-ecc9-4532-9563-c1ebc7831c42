"""
任务业务逻辑服务

负责任务的创建、查询、管理等业务逻辑
"""

import uuid

from fastapi import HTTPException

from app.config.enums import ExecutionStatus
from app.config.settings import settings
from app.models.task import Task
from app.schemas.task import (
    TaskFilesResponse,
    TaskResultResponse,
    TaskStatusResponse,
)
from app.utils.timezone import get_shanghai_now


async def create_task(task_description: str) -> str:
    """
    创建新任务

    Args:
        task_description: 任务描述

    Returns:
        str: 任务ID
    """
    ticket_id = str(uuid.uuid4().hex)
    now = get_shanghai_now()

    await Task.create(
        ticket_id=ticket_id,
        status=ExecutionStatus.PENDING,
        task_description=task_description,
        replica_id=settings.replica_id,
        created_at=now,
        updated_at=now,
    )

    return ticket_id


async def get_task_result(ticket_id: str) -> TaskResultResponse:
    """
    获取任务的详细结果

    Args:
        ticket_id: 任务ID

    Returns:
        TaskResultResponse: 任务结果响应
    """
    task = await Task.get_or_none(ticket_id=ticket_id)

    if not task:
        return TaskResultResponse(ticket_id=ticket_id, status="NOT_FOUND", result=None, error_message="任务不存在")

    return TaskResultResponse(
        ticket_id=task.ticket_id,
        status=task.status,
        step_log=task.step_log,
        result=task.result,
        error_message=task.error_message,
        selenium_session_id=task.selenium_session_id,
        replica_id=task.replica_id,
        created_at=task.created_at,
        updated_at=task.updated_at,
        video_file_paths=task.video_file_paths,
        screenshot_file_paths=task.screenshot_file_paths,
        download_file_paths=task.download_file_paths,
    )


async def get_task_status(ticket_id: str) -> TaskStatusResponse:
    """
    获取任务状态（轻量级接口）

    Args:
        ticket_id: 任务ID

    Returns:
        TaskStatusResponse: 任务状态响应
    """
    task = await Task.get_or_none(ticket_id=ticket_id)

    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    return TaskStatusResponse(ticket_id=task.ticket_id, status=task.status, replica_id=task.replica_id)


async def get_task_files(ticket_id: str) -> TaskFilesResponse:
    """
    获取任务的所有文件信息

    直接从数据库返回文件路径作为URL列表，不进行文件验证和详细信息获取

    Args:
        ticket_id: 任务ID

    Returns:
        TaskFilesResponse: 任务文件响应，包含三个URL列表

    Raises:
        HTTPException: 任务不存在时抛出404错误
    """
    # 查询任务是否存在
    task = await Task.get_or_none(ticket_id=ticket_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 处理视频文件URL列表
    video_urls = []
    if task.video_file_paths:
        video_urls = list(task.video_file_paths)

    # 处理截图文件URL列表
    screenshots_urls = []
    if task.screenshot_file_paths:
        screenshots_urls = list(task.screenshot_file_paths)

    # 处理下载文件URL列表
    file_urls = []
    if task.download_file_paths:
        file_urls = list(task.download_file_paths)

    return TaskFilesResponse(
        ticket_id=ticket_id,
        video_urls=video_urls,
        screenshots_urls=screenshots_urls,
        file_urls=file_urls,
    )
