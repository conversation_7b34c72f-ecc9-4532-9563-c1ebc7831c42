"""
云服务登录策略模式实现

提供可扩展的云服务登录策略，支持不同类型的云服务提供商
"""

from abc import ABC, abstractmethod
from typing import Any

from app.utils.log import logger


class CloudLoginStrategy(ABC):
    """
    云服务登录策略抽象基类

    定义了云服务登录的通用接口，具体的云服务提供商需要继承此类
    并实现get_initial_actions方法
    """

    @abstractmethod
    def get_initial_actions(self, username: str | None, password: str | None) -> list[dict[str, Any]]:
        """
        获取云服务特定的初始化登录动作列表

        参数:
            username: 用户名
            password: 密码

        返回:
            初始化登录动作列表
        """
        pass

    @abstractmethod
    def get_provider_name(self) -> str:
        """
        获取云服务提供商名称

        返回:
            云服务提供商名称字符串
        """
        pass

    @abstractmethod
    def get_portal_url(self) -> str:
        """
        获取云服务门户URL

        返回:
            云服务门户URL字符串
        """
        pass


class EcloudLoginStrategy(CloudLoginStrategy):
    """
    移动云登录策略

    处理移动云相关的登录逻辑，包括导航到门户和登录操作
    """

    def get_initial_actions(self, username: str | None, password: str | None) -> list[dict[str, Any]]:
        """
        获取移动云的初始化登录动作

        参数:
            username: 用户名
            password: 密码

        返回:
            包含登录动作的列表
        """
        initial_actions = []

        # 移动云有凭据时才添加登录动作
        if username and password:
            initial_actions.append(
                {
                    "login_ecloud": {
                        "username": username,
                        "password": password,
                    }
                }
            )
            logger.info(f"已添加{self.get_provider_name()}登录动作到初始化操作")
        else:
            logger.warning(f"{self.get_provider_name()}没有提供凭据，将以未登录状态继续")

        return initial_actions

    def get_provider_name(self) -> str:
        """获取云服务提供商名称"""
        return "移动云"

    def get_portal_url(self) -> str:
        """获取移动云门户URL"""
        return "https://ecloud.10086.cn"


class BaiduCloudLoginStrategy(CloudLoginStrategy):
    """
    百度云登录策略

    处理百度云相关的登录逻辑
    """

    def get_initial_actions(self, username: str | None, password: str | None) -> list[dict[str, Any]]:
        """
        获取百度云的初始化登录动作

        参数:
            username: 用户名
            password: 密码

        返回:
            包含登录动作的列表
        """
        initial_actions = []

        if username and password:
            # 百度云登录动作 - 待实现具体的登录逻辑
            initial_actions.append(
                {
                    "login_baidu_cloud": {
                        "username": username,
                        "password": password,
                    }
                }
            )
            logger.info(f"已添加{self.get_provider_name()}登录动作到初始化操作")
        else:
            logger.warning(f"{self.get_provider_name()}没有提供凭据，将以未登录状态继续")

        return initial_actions

    def get_provider_name(self) -> str:
        """获取云服务提供商名称"""
        return "百度云"

    def get_portal_url(self) -> str:
        """获取百度云门户URL"""
        return "https://console.bce.baidu.com"


class HuaweiCloudLoginStrategy(CloudLoginStrategy):
    """
    华为云登录策略

    处理华为云相关的登录逻辑
    """

    def get_initial_actions(self, username: str | None, password: str | None) -> list[dict[str, Any]]:
        """
        获取华为云的初始化登录动作

        参数:
            username: 用户名
            password: 密码

        返回:
            包含登录动作的列表
        """
        initial_actions = []

        if username and password:
            # 华为云登录动作 - 待实现具体的登录逻辑
            initial_actions.append(
                {
                    "login_huawei_cloud": {
                        "username": username,
                        "password": password,
                    }
                }
            )
            logger.info(f"已添加{self.get_provider_name()}登录动作到初始化操作")
        else:
            logger.warning(f"{self.get_provider_name()}没有提供凭据，将以未登录状态继续")

        return initial_actions

    def get_provider_name(self) -> str:
        """获取云服务提供商名称"""
        return "华为云"

    def get_portal_url(self) -> str:
        """获取华为云门户URL"""
        return "https://console.huaweicloud.com"


class AlibabaCloudLoginStrategy(CloudLoginStrategy):
    """
    阿里云登录策略

    处理阿里云相关的登录逻辑
    """

    def get_initial_actions(self, username: str | None, password: str | None) -> list[dict[str, Any]]:
        """
        获取阿里云的初始化登录动作

        参数:
            username: 用户名
            password: 密码

        返回:
            包含登录动作的列表
        """
        initial_actions = []

        if username and password:
            # 阿里云登录动作 - 待实现具体的登录逻辑
            initial_actions.append(
                {
                    "login_alibaba_cloud": {
                        "username": username,
                        "password": password,
                    }
                }
            )
            logger.info(f"已添加{self.get_provider_name()}登录动作到初始化操作")
        else:
            logger.warning(f"{self.get_provider_name()}没有提供凭据，将以未登录状态继续")

        return initial_actions

    def get_provider_name(self) -> str:
        """获取云服务提供商名称"""
        return "阿里云"

    def get_portal_url(self) -> str:
        """获取阿里云门户URL"""
        return "https://ecs.console.aliyun.com"


class DefaultLoginStrategy(CloudLoginStrategy):
    """
    默认登录策略

    用于处理通用的登录场景，通常不需要特殊的登录操作
    """

    def get_initial_actions(self, username: str | None, password: str | None) -> list[dict[str, Any]]:
        """
        获取默认的初始化登录动作

        参数:
            username: 用户名（未使用）
            password: 密码（未使用）

        返回:
            空的初始化动作列表
        """
        # 默认策略不使用用户名和密码参数
        _ = username, password  # 显式标记参数为未使用
        logger.info("默认登录策略，不添加特殊登录操作")
        return []

    def get_provider_name(self) -> str:
        """获取云服务提供商名称"""
        return "默认服务"

    def get_portal_url(self) -> str:
        """获取默认门户URL"""
        return ""


class CloudLoginStrategyFactory:
    """
    云服务登录策略工厂类

    根据云服务提供商类型创建相应的登录策略实例
    """

    # 策略注册表
    _strategies = {
        "ecloud": EcloudLoginStrategy,
        "baidu_cloud": BaiduCloudLoginStrategy,
        "huawei_cloud": HuaweiCloudLoginStrategy,
        "alibaba_cloud": AlibabaCloudLoginStrategy,
        "default": DefaultLoginStrategy,
    }

    @classmethod
    def create_strategy(cls, provider_type: str) -> CloudLoginStrategy:
        """
        创建云服务登录策略实例

        参数:
            provider_type: 云服务提供商类型标识符

        返回:
            对应的云服务登录策略实例

        异常:
            ValueError: 当云服务提供商类型不支持时抛出
        """
        if provider_type not in cls._strategies:
            logger.warning(f"不支持的云服务提供商类型: {provider_type}，使用默认策略")
            provider_type = "default"

        strategy_class = cls._strategies[provider_type]
        strategy_instance = strategy_class()

        logger.debug(f"创建云服务登录策略: {strategy_instance.get_provider_name()}")
        return strategy_instance

    @classmethod
    def register_strategy(cls, provider_type: str, strategy_class: type):
        """
        注册新的云服务登录策略

        参数:
            provider_type: 云服务提供商类型标识符
            strategy_class: 策略类
        """
        if not issubclass(strategy_class, CloudLoginStrategy):
            raise ValueError(f"策略类必须继承自CloudLoginStrategy: {strategy_class}")

        cls._strategies[provider_type] = strategy_class
        logger.info(f"注册新的云服务登录策略: {provider_type} -> {strategy_class.__name__}")

    @classmethod
    def get_supported_provider_types(cls) -> list[str]:
        """
        获取支持的云服务提供商类型列表

        返回:
            支持的云服务提供商类型列表
        """
        return list(cls._strategies.keys())


def create_cloud_login_strategy(provider_type: str = "ecloud") -> CloudLoginStrategy:
    """
    创建云服务登录策略的便捷函数

    参数:
        provider_type: 云服务提供商类型标识符，默认为'ecloud'（移动云）

    返回:
        对应的云服务登录策略实例
    """
    return CloudLoginStrategyFactory.create_strategy(provider_type)
