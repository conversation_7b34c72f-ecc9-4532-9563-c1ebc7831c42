"""
脚本执行相关的 API 路由

包含所有脚本执行管理的 HTTP 接口
"""

import asyncio

from fastapi import APIRouter, HTTPException

from app.schemas.script import (
    ScriptExecuteRequest,
    ScriptExecuteResponse,
    ScriptResultResponse,
    ScriptStatusResponse,
    ScriptTerminateResponse,
)
from app.services.script_execution_service import (
    execute_script,
    terminate_script_process,
)
from app.services.script_service import (
    create_script,
    get_script_result,
    get_script_status,
)
from app.utils.log import logger

router = APIRouter()


@router.post("/execute", response_model=ScriptExecuteResponse)
async def execute_script_endpoint(request: ScriptExecuteRequest):
    """
    执行Python脚本

    流程：
    1. 生成唯一的脚本ID
    2. 在数据库中创建脚本记录
    3. 异步执行脚本
    4. 立即返回脚本ID和状态

    Args:
        request: 脚本执行请求，包含script、username、password等

    Returns:
        ScriptExecuteResponse: 包含script_id、status、message
    """
    try:
        # 创建脚本记录
        ticket_id = await create_script(script_content=request.script)

        # 异步执行脚本（不等待完成）
        asyncio.create_task(execute_script(ticket_id=ticket_id, username=request.username, password=request.password))

        logger.info(f"脚本执行任务已创建: ticket_id={ticket_id}")

        return ScriptExecuteResponse(ticket_id=ticket_id, status="PENDING", message="脚本已创建并开始执行")

    except Exception as e:
        logger.error(f"创建脚本执行任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建脚本执行任务失败: {str(e)}")


@router.get("/result/{ticket_id}", response_model=ScriptResultResponse)
async def get_script_result_endpoint(ticket_id: str):
    """
    获取脚本的详细执行结果

    返回完整的脚本信息，包括：
    - 脚本状态
    - 脚本内容
    - 标准输出和错误输出
    - 返回码
    - 执行时间信息
    - 副本信息

    Args:
        ticket_id: 脚本唯一标识符

    Returns:
        ScriptResultResponse: 脚本详细执行结果
    """
    try:
        return await get_script_result(ticket_id)
    except Exception as e:
        logger.error(f"获取脚本结果失败: ticket_id={ticket_id}, 错误: {e}")
        raise HTTPException(status_code=404, detail=f"脚本不存在或获取结果失败: {str(e)}")


@router.get("/status/{ticket_id}", response_model=ScriptStatusResponse)
async def get_script_status_endpoint(ticket_id: str):
    """
    获取脚本状态（轻量级接口）

    只返回基本的状态信息，不包含执行结果数据
    适合频繁轮询使用

    Args:
        ticket_id: 脚本唯一标识符

    Returns:
        ScriptStatusResponse: 脚本基本状态信息
    """
    try:
        return await get_script_status(ticket_id)
    except Exception as e:
        logger.error(f"获取脚本状态失败: ticket_id={ticket_id}, 错误: {e}")
        raise HTTPException(status_code=404, detail=f"脚本不存在或获取状态失败: {str(e)}")


@router.get("/terminate/{ticket_id}", response_model=ScriptTerminateResponse)
async def terminate_script_endpoint(ticket_id: str):
    """
    终止脚本执行

    终止正在执行或等待执行的脚本任务：
    1. 检查脚本状态，只有 PENDING 或 RUNNING 状态可以被终止
    2. 如果脚本正在运行，终止对应的进程
    3. 释放占用的 Selenium 会话资源
    4. 将脚本状态更新为 CANCELLED
    5. 记录终止时间和原因

    Args:
        ticket_id: 脚本唯一标识符

    Returns:
        ScriptTerminateResponse: 终止操作结果，包含是否成功和详细信息
    """
    try:
        result = await terminate_script_process(ticket_id)

        if not result.success:
            # 根据错误信息返回合适的HTTP状态码
            if "脚本不存在" in result.message:
                raise HTTPException(status_code=404, detail=result.message)
            elif "无法终止" in result.message:
                raise HTTPException(status_code=400, detail=result.message)
            else:
                raise HTTPException(status_code=500, detail=result.message)

        logger.info(f"脚本终止成功: ticket_id={ticket_id}")
        return result

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"终止脚本接口异常: ticket_id={ticket_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"终止脚本时发生异常: {str(e)}")
