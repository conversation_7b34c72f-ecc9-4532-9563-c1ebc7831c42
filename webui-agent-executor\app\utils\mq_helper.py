"""
RabbitMQ消息发送工具模块

提供统一的消息发送接口，用于子进程中向RabbitMQ发送各种类型的消息
"""

from typing import Any

from app.config.enums import ExecutionStatus
from app.services.mq_producer import cleanup_producer, get_producer
from app.utils.log import logger


def send_step_log_message(ticket_id: str, message: str, append: bool = True, raise_on_error: bool = False) -> None:
    """
    在子进程中发送步骤日志消息到RabbitMQ

    Args:
        ticket_id: 任务ID
        message: 日志消息
        append: 是否追加到现有日志（True）还是覆盖（False），默认为追加
        raise_on_error: 是否在错误时抛出异常，默认为False（日志更新失败不影响主要逻辑）
    """
    try:
        producer = get_producer()
        response = producer.send_append_log(ticket_id=ticket_id, message_text=message, append=append)

        if response.success:
            logger.debug(f"子进程发送步骤日志消息成功: {ticket_id}")
        else:
            logger.error(f"子进程发送步骤日志消息失败: {ticket_id}, {response.error}")
            if raise_on_error:
                raise RuntimeError(f"发送步骤日志消息失败: {response.error}")
    except Exception as e:
        logger.error(f"子进程发送步骤日志消息异常: {ticket_id}, {str(e)}")
        if raise_on_error:
            raise


def send_task_status_message(
    ticket_id: str, status: ExecutionStatus, result_or_error: Any = None, step_log_message: str = None
):
    """
    在子进程中发送任务状态更新消息到RabbitMQ

    Args:
        ticket_id: 任务ID
        status: 任务状态
        result_or_error: 成功时的结果或失败时的错误信息
        step_log_message: 可选的步骤日志消息

    Raises:
        RuntimeError: 当消息发送失败时

    Note:
        这是核心的状态更新函数，异常会被抛出给调用方处理
    """
    try:
        producer = get_producer()
        response = producer.send_update_status(
            ticket_id=ticket_id, status=status, result_or_error=result_or_error, step_log_message=step_log_message
        )

        if response.success:
            logger.info(f"子进程发送任务状态消息成功: {ticket_id} -> {status}")
        else:
            error_msg = f"发送任务状态消息失败: {response.error}"
            logger.error(f"子进程更新失败 - {error_msg}")
            raise RuntimeError(error_msg)

    except Exception as e:
        logger.error(f"子进程发送状态消息异常: {ticket_id}, {str(e)}")
        # 重新抛出异常，让调用方知道更新失败
        raise


def send_selenium_session_message(ticket_id: str, session_id: str):
    """
    发送Selenium会话消息到RabbitMQ

    Args:
        ticket_id: 任务ID
        session_id: Selenium会话ID
    """
    try:
        producer = get_producer()
        response = producer.send_selenium_session(ticket_id, session_id)

        if response.success:
            logger.info(f"子进程发送Selenium会话消息成功: {ticket_id} -> {session_id}")
        else:
            error_msg = f"发送Selenium会话消息失败: {response.error}"
            logger.error(f"子进程更新失败 - {error_msg}")
            raise RuntimeError(error_msg)

    except Exception as e:
        logger.error(f"子进程发送Selenium会话消息异常: {ticket_id}, {str(e)}")
        # 重新抛出异常，让调用方知道更新失败
        raise


def send_file_paths_update_message(ticket_id: str, file_paths_data: dict, raise_on_error: bool = False) -> None:
    """
    在子进程中发送文件路径更新消息到RabbitMQ

    Args:
        ticket_id: 任务ID
        file_paths_data: 文件路径数据字典
        raise_on_error: 是否在错误时抛出异常，默认为False
    """
    try:
        producer = get_producer()
        response = producer.send_update_file_paths(ticket_id=ticket_id, file_paths_data=file_paths_data)

        if response.success:
            logger.debug(f"文件路径更新消息发送成功: {ticket_id}")
        else:
            error_msg = f"文件路径更新消息发送失败: {ticket_id}, 错误: {response.error}"
            logger.error(error_msg)
            if raise_on_error:
                raise Exception(error_msg)

    except Exception as e:
        error_msg = f"发送文件路径更新消息时出错: {ticket_id}, 错误: {str(e)}"
        logger.error(error_msg)
        if raise_on_error:
            raise


def send_append_screenshot_path_message(ticket_id: str, screenshot_path: str, raise_on_error: bool = False) -> None:
    """
    在子进程中发送追加截图路径消息到RabbitMQ

    Args:
        ticket_id: 任务ID
        screenshot_path: 截图文件路径
        raise_on_error: 是否在错误时抛出异常，默认为False
    """
    try:
        producer = get_producer()
        response = producer.send_append_screenshot_path(ticket_id=ticket_id, screenshot_path=screenshot_path)

        if response.success:
            logger.info(f"追加截图路径消息发送成功: {ticket_id} -> {screenshot_path}")
        else:
            error_msg = f"追加截图路径消息发送失败: {ticket_id}, 错误: {response.error}"
            logger.error(error_msg)
            if raise_on_error:
                raise Exception(error_msg)

    except Exception as e:
        error_msg = f"发送追加截图路径消息时出错: {ticket_id}, 错误: {str(e)}"
        logger.error(error_msg)
        if raise_on_error:
            raise


def send_append_video_path_message(ticket_id: str, video_path: str, raise_on_error: bool = False):
    """发送追加视频路径消息"""
    try:
        producer = get_producer()
        response = producer.send_append_video_path(ticket_id, video_path)

        if response.success:
            logger.info(f"追加视频路径消息发送成功: {ticket_id} -> {video_path}")
        else:
            error_msg = f"追加视频路径消息发送失败: {ticket_id}, 错误: {response.error}"
            logger.error(error_msg)
            if raise_on_error:
                raise Exception(error_msg)

    except Exception as e:
        error_msg = f"发送追加视频路径消息时出错: {ticket_id}, 错误: {str(e)}"
        logger.error(error_msg)
        if raise_on_error:
            raise


def send_append_file_path_message(ticket_id: str, file_path: str, raise_on_error: bool = False):
    """发送追加下载文件路径消息"""
    try:
        producer = get_producer()
        response = producer.send_append_file_path(ticket_id, file_path)

        if response.success:
            logger.info(f"追加下载文件路径消息发送成功: {ticket_id} -> {file_path}")
        else:
            error_msg = f"追加下载文件路径消息发送失败: {ticket_id}, 错误: {response.error}"
            logger.error(error_msg)
            if raise_on_error:
                raise Exception(error_msg)

    except Exception as e:
        error_msg = f"发送追加下载文件路径消息时出错: {ticket_id}, 错误: {str(e)}"
        logger.error(error_msg)
        if raise_on_error:
            raise


def cleanup_mq_producer() -> None:
    """
    清理RabbitMQ生产者资源

    用于子进程结束时清理资源
    """
    try:
        cleanup_producer()
        logger.debug("RabbitMQ生产者清理完成")
    except Exception as e:
        logger.debug(f"清理RabbitMQ生产者时出错: {str(e)}")
