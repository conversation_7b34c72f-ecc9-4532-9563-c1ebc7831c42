# WebUI 智能体执行服务

[![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115.12+-green.svg)](https://fastapi.tiangolo.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个基于 FastAPI 和 browser-use 框架构建的分布式浏览器自动化执行系统，支持多副本部署、任务队列管理、实时监控和录制功能。

## 🎯 核心功能

- **🤖 智能浏览器自动化**：基于 browser-use 框架的智能网页操作
- **🚀 分布式架构**：支持 Kubernetes 多副本水平扩展
- **📊 实时监控**：任务执行状态实时跟踪和日志记录
- **🎬 录制回放**：生成视频和执行步骤记录
- **⚡ 异步消息处理**：RabbitMQ 消息队列处理数据库操作
- **📦 对象存储**：支持 MinIO 对象存储，可选本地文件存储

## 🏗️ 项目结构

```
AINT_be_fastapi/
├── webui_agent/webui-agent-executor/          # 主应用目录
│   ├── app/                                   # FastAPI 应用核心
│   │   ├── api/                              # API 路由层
│   │   │   ├── dependencies.py               # 依赖注入配置
│   │   │   └── routes/                       # 路由模块
│   │   │       └── task.py                   # 任务相关 API
│   │   ├── config/                           # 配置管理
│   │   │   └── settings.py                   # 应用配置设置
│   │   ├── models/                           # 数据模型
│   │   │   └── task.py                       # 任务数据模型
│   │   ├── schemas/                          # Pydantic 模式
│   │   │   ├── task.py                       # 任务请求/响应模式
│   │   │   └── mq_message.py                 # 消息队列模式
│   │   ├── services/                         # 业务逻辑层
│   │   │   ├── task_service.py               # 任务业务逻辑
│   │   │   ├── process_service.py            # 进程管理服务
│   │   │   ├── mq_consumer.py                # 消息队列消费者
│   │   │   ├── mq_producer.py                # 消息队列生产者
│   │   │   └── callback_service.py           # 回调服务
│   │   ├── utils/                            # 工具模块
│   │   │   ├── log.py                        # 日志工具
│   │   │   ├── crypto.py                     # 加密工具
│   │   │   ├── timezone.py                   # 时区工具
│   │   │   ├── browser_use_deepseek.py       # DeepSeek LLM 集成
│   │   │   ├── custom_actions.py             # 自定义动作
│   │   │   ├── selenium_grid.py              # Selenium Grid 工具
│   │   │   └── mq_helper.py                  # 消息队列助手
│   │   └── main.py                           # FastAPI 应用入口
│   ├── recordings/                           # 录制文件存储（本地存储）
│   │   ├── video/                            # 视频录制
│   │   ├── screenshot/                       # 截图
│   │   └── file/                             # 文件下载
│   ├── migrations/                           # 数据库迁移
│   ├── examples/                             # 示例代码
│   ├── keys/                                 # 密钥文件
│   ├── main.py                               # 应用启动入口
│   ├── pyproject.toml                        # 项目配置
│   ├── Dockerfile                            # Docker 构建文件
│   ├── docker-compose.yml                    # Docker Compose 配置
│   └── k8s-deployment.yaml                   # Kubernetes 部署配置
├── marker_service/                           # 标记服务（独立模块）
├── logs/                                     # 日志文件
├── uploads/                                  # 上传文件
├── static/                                   # 静态文件
└── results/                                  # 结果文件
```

## 🛠️ 技术栈

### 核心框架

- **FastAPI** (≥0.115.12) - 现代高性能 Web 框架
- **Python** (≥3.12) - 编程语言
- **Uvicorn** (≥0.34.3) - ASGI 服务器

### 智能体与自动化

- **browser-use** (0.5.5) - 智能浏览器自动化框架
- **Selenium Grid** - 分布式浏览器管理
- **PaddleOCR** (≥3.0.2) - 光学字符识别

### 数据存储与消息队列

- **Tortoise ORM** (≥0.25.1) - 异步 ORM 框架
- **PostgreSQL** (12.0+) - 关系型数据库
- **RabbitMQ** (3.12+) - 消息队列系统
- **Pika** (≥1.3.0) - RabbitMQ Python 客户端
- **MinIO** - 对象存储服务，用于存储录制文件和截图

### 配置与工具

- **Pydantic Settings** (≥2.0.0) - 配置管理
- **Loguru** (≥0.7.3) - 结构化日志
- **Cryptography** (≥41.0.0) - 加密库
- **uv** - Python 包管理器

## 📦 模块功能介绍

### 🎯 核心业务模块

#### 1. API 路由层 (`app/api/`)

- **routes/task.py**: 任务管理 API，提供任务创建、查询、终止等接口
- **dependencies.py**: FastAPI 依赖注入，数据库连接管理

#### 2. 业务逻辑层 (`app/services/`)

- **task_service.py**: 任务业务逻辑，负责任务的创建和查询
- **process_service.py**: 进程管理服务，处理浏览器自动化任务的执行
- **mq_consumer.py**: 消息队列消费者，异步处理数据库操作
- **mq_producer.py**: 消息队列生产者，发送任务状态更新消息

#### 3. 数据层 (`app/models/` & `app/schemas/`)

- **models/task.py**: Tortoise ORM 任务数据模型
- **schemas/task.py**: Pydantic 请求/响应模式定义
- **schemas/mq_message.py**: 消息队列数据模式

### 🔧 支撑模块

#### 4. 配置管理 (`app/config/`)

- **settings.py**: 统一的应用配置管理，支持环境变量

#### 5. 工具模块 (`app/utils/`)

- **log.py**: 基于 Loguru 的日志系统
- **browser_use_deepseek.py**: DeepSeek LLM 模型集成
- **selenium_grid.py**: Selenium Grid 连接管理
- **custom_actions.py**: 自定义浏览器操作动作
- **timezone.py**: 时区处理工具
- **crypto.py**: 加密解密工具

## 🚀 快速开始

### 环境要求

- Python 3.12+
- PostgreSQL 12.0+
- RabbitMQ 3.12+
- MinIO (可选，用于对象存储)
- Selenium Grid (可选)

### 安装步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd AINT_be_fastapi
```

2. **安装依赖**

```bash
# 使用 uv 包管理器
pip install uv
cd webui_agent/webui-agent-executor
uv sync --all-extras
```

3. **配置环境变量**

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env
```

4. **启动服务**

```bash
# 开发环境
uv run python main.py

# 生产环境
uv run uvicorn app.main:app --host 0.0.0.0 --port 8080
```

5. **代码语法检查**

```bash
ruff check .
```

```bash
# 实时检查
ruff check --watch .
```

6. **代码类型检查**

```bash
pyright .
```

```bash
# 实时检查
pyright --watch
```

### Docker 部署

```bash
# 构建镜像
docker build -t webui-agent-executor .

# 运行容器
docker run -d -p 8080:8080 \
  -e DATABASE_URL="******************************/db" \
  -e RABBITMQ_HOST="rabbitmq-host" \
  -e MINIO_ENDPOINT="minio-host:9000" \
  -e MINIO_ACCESS_KEY="minioadmin" \
  -e MINIO_SECRET_KEY="minioadmin" \
  webui-agent-executor
```

### Kubernetes 部署

```bash
# 部署到 Kubernetes
kubectl apply -f k8s-deployment.yaml

# 扩容副本
kubectl scale deployment browser-use-app --replicas=5
```

## 📚 API 文档

启动服务后，访问以下地址查看 API 文档：

- **Swagger UI**: <http://localhost:8080/docs>
- **ReDoc**: <http://localhost:8080/redoc>

### 主要 API 接口

- `POST /v1/query` - 创建新任务
- `GET /v1/result/{ticket_id}` - 获取任务结果
- `GET /v1/status/{ticket_id}` - 获取任务状态
- `GET /v1/terminate/{ticket_id}` - 终止任务

## 🔧 配置说明

主要配置项位于 `app/config/settings.py`，支持通过环境变量覆盖：

```python
# 数据库配置
DATABASE_URL = "postgres://user:pass@localhost:5432/webui_agent"

# RabbitMQ 配置
RABBITMQ_HOST = "localhost"
RABBITMQ_PORT = 5672
RABBITMQ_PASSWORD=""

# MinIO 对象存储配置
MINIO_ENDPOINT = "*************:9000"      # MinIO 服务端点
MINIO_ACCESS_KEY = "minioadmin"             # 访问密钥
MINIO_SECRET_KEY = "minioadmin"             # 秘密密钥
MINIO_BUCKET_NAME = "webui-agent-recordings" # 存储桶名称
MINIO_SECURE = False                        # 是否使用 HTTPS
MINIO_REGION = "us-east-1"                  # 区域设置
MINIO_URL_EXPIRY = 3600                     # 文件URL过期时间（秒）
MINIO_ENABLED = True                        # 是否启用MinIO存储

# LLM 配置
OPENAI_API_KEY = "your-api-key"
OPENAI_BASE_URL = "https://api.deepseek.com"
```

### MinIO 对象存储配置

系统支持两种文件存储方式：

1. **本地存储**（默认）：文件存储在 `recordings/` 目录下
2. **MinIO 对象存储**：文件存储在 MinIO 服务器上

#### MinIO 部署

使用 Docker 快速部署 MinIO：

```bash
# 启动 MinIO 服务
docker run -d \
  --name minio \
  -p 9000:9000 \
  -p 9001:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  -v minio_data:/data \
  minio/minio server /data --console-address ":9001"
```

访问 MinIO 控制台：<http://localhost:9001>

#### 配置说明

- `MINIO_ENABLED=True`：启用 MinIO 存储
- `MINIO_ENABLED=False`：使用本地存储
- 存储桶会在首次使用时自动创建
- 支持文件 URL 预签名，可设置过期时间

## 🧪 测试

```bash
# 运行测试
uv run pytest

# 运行特定测试
uv run pytest tests/test_task_service.py
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题，请通过以下方式联系：

- 提交 Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <<EMAIL>>
