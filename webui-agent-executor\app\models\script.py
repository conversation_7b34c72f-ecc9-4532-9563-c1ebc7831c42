"""
脚本数据库模型

定义脚本执行在数据库中的结构和字段
"""

from tortoise import fields
from tortoise.models import Model

from app.config.enums import ExecutionStatus


class Script(Model):
    """
    脚本数据库模型

    存储脚本执行的历史记录和状态信息
    """

    # 自增主键ID
    id = fields.IntField(pk=True, description="主键ID")

    # 脚本唯一标识符（与task表保持一致）
    ticket_id = fields.CharField(max_length=50, unique=True, description="脚本唯一标识符")

    # 脚本内容
    script_content = fields.TextField(description="要执行的Python脚本内容")

    # 脚本执行状态：PENDING(等待) -> RUNNING(运行中) -> SUCCESS(成功)/FAILURE(失败)/CANCELLED(取消)
    status = fields.CharEnumField(ExecutionStatus, default=ExecutionStatus.PENDING, description="脚本执行状态")

    # 脚本执行结果（标准输出）
    stdout = fields.TextField(null=True, description="脚本执行的标准输出")

    # 脚本执行错误信息（标准错误）
    stderr = fields.TextField(null=True, description="脚本执行的标准错误输出")

    # 脚本执行返回码
    return_code = fields.IntField(null=True, description="脚本执行的返回码")

    # 处理该脚本的副本ID
    replica_id = fields.CharField(max_length=100, null=True, description="处理该脚本的副本ID")

    # Selenium会话ID
    selenium_session_id = fields.CharField(max_length=50, null=True, description="Selenium会话ID")

    # 脚本创建时间
    created_at = fields.DatetimeField(auto_now_add=True, description="脚本创建时间")

    # 脚本最后更新时间
    updated_at = fields.DatetimeField(auto_now=True, description="脚本最后更新时间")

    # 脚本开始执行时间
    started_at = fields.DatetimeField(null=True, description="脚本开始执行时间")

    # 脚本执行完成时间
    completed_at = fields.DatetimeField(null=True, description="脚本执行完成时间")

    class Meta:
        table = "script"
        table_description = "脚本数据库模型"
