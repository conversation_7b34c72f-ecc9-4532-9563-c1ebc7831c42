import logging
import sys
from pathlib import Path

from loguru import logger as loguru_logger

from app.config.settings import settings


class InterceptHandler(logging.Handler):
    """
    拦截标准 logging 模块的日志，重定向到 loguru
    """

    def emit(self, record):
        # 获取对应的 loguru 级别
        try:
            level = loguru_logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # 找到调用者的 frame
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        loguru_logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


class LogConfig:
    def __init__(self, debug: bool = False):
        self.debug = debug
        self.level = "DEBUG" if self.debug else "INFO"
        self.log_dir = Path(settings.log_dir)

        # 确保日志目录存在
        self.log_dir.mkdir(parents=True, exist_ok=True)

    def setup_logger(self):
        # 移除默认的 handler
        loguru_logger.remove()

        # 添加控制台输出
        loguru_logger.add(
            sink=sys.stdout,
            level=self.level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <yellow>T{thread}</yellow> | <cyan>{name}</cyan> - <level>{message}</level>",
            colorize=True,
        )

        # 添加文件输出 - 所有组件日志都输出到 app.log
        loguru_logger.add(
            sink=self.log_dir / "app.log",
            level=self.level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | T{thread} | {name} - {message}",
            rotation="20 MB",
            retention="7 days",
            compression="zip",
            encoding="utf-8",
        )

        # 添加错误日志文件（只记录 ERROR 及以上级别）
        loguru_logger.add(
            sink=self.log_dir / "error.log",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | T{thread} | {name} - {message}",
            rotation="5 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8",
        )

        # 设置标准 logging 拦截器
        self._setup_logging_intercept()

        return loguru_logger

    def _setup_logging_intercept(self):
        """
        设置标准 logging 模块的拦截器，将所有日志重定向到 loguru
        """
        # 获取根日志器
        root_logger = logging.getLogger()

        # 移除所有现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 添加拦截处理器
        intercept_handler = InterceptHandler()
        root_logger.addHandler(intercept_handler)

        # 设置根日志级别
        root_logger.setLevel(self.level)

        # 特别设置重要的第三方库日志级别，确保它们都被拦截
        important_loggers = [
            "browser_use",
            "playwright",
            "asyncio",
            "httpx",
            "fastapi",
            "uvicorn",
            "uvicorn.error",
            "uvicorn.access",
            "tortoise",
        ]

        for logger_name in important_loggers:
            logger_instance = logging.getLogger(logger_name)
            logger_instance.handlers = []
            logger_instance.addHandler(intercept_handler)
            logger_instance.setLevel(self.level)
            logger_instance.propagate = False


loggin = LogConfig(debug=settings.debug)
logger = loggin.setup_logger()
