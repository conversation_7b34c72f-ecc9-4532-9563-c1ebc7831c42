"""
Cookie 管理器模块

提供完整的 Cookie 生命周期管理功能，使用 CDP 原生方法
"""

import json
import time
from pathlib import Path
from typing import Any

from browser_use.browser import BrowserSession

from app.schemas.cookie import CookieModel
from app.utils.log import logger


class CookieManager:
    """Cookie 生命周期管理器 - 使用 CDP 原生方法"""

    def __init__(self, browser_session: BrowserSession):
        """
        初始化 Cookie 管理器

        Args:
            browser_session: Browser Use 浏览器会话对象
        """
        self.browser_session = browser_session
        self.cdp_session = None
        self.cookies_store: dict[str, dict[str, Any]] = {}  # 存储当前管理的 cookies
        self.is_network_enabled = False
        self._current_url = None

    async def initialize(self) -> None:
        """初始化 CDP session 和启用 Network 域"""
        try:
            # 获取当前页面
            page = await self.browser_session.get_current_page()
            if not page:
                raise RuntimeError("无法获取当前页面")

            # 获取当前页面的 URL
            self._current_url = page.url
            logger.info(f"Cookie 管理器初始化，当前页面: {self._current_url}")

            # 创建 CDP session
            self.cdp_session = await page.context.new_cdp_session(page)
            if not self.cdp_session:
                raise RuntimeError("无法创建 CDP session")

            # 启用 Network 域
            await self.cdp_session.send("Network.enable")
            self.is_network_enabled = True
            logger.info("CDP Network 域已启用")

        except Exception as e:
            error_msg = f"Cookie 管理器初始化失败: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def _ensure_initialized(self) -> None:
        """确保 Cookie 管理器已初始化"""
        if not self.cdp_session or not self.is_network_enabled:
            await self.initialize()

    def _convert_cookie_to_cdp_format(self, cookie: CookieModel) -> dict[str, Any]:
        """
        将 CookieModel 转换为 CDP Network.setCookie 所需的格式

        Args:
            cookie: Cookie 模型对象

        Returns:
            dict: CDP 格式的 cookie 参数
        """
        # 构建基础 URL（CDP 需要 URL 而不是 domain）
        protocol = "https" if cookie.secure else "http"
        url = f"{protocol}://{cookie.domain.lstrip('.')}{cookie.path}"

        cdp_cookie = {
            "name": cookie.name,
            "value": cookie.value,
            "url": url,
            "domain": cookie.domain,
            "path": cookie.path,
        }

        # 添加可选字段
        if cookie.expires is not None:
            cdp_cookie["expires"] = cookie.expires
        if cookie.httpOnly is not None:
            cdp_cookie["httpOnly"] = cookie.httpOnly
        if cookie.secure is not None:
            cdp_cookie["secure"] = cookie.secure
        if cookie.sameSite is not None:
            cdp_cookie["sameSite"] = cookie.sameSite

        return cdp_cookie

    async def set_cookie(self, cookie: CookieModel) -> bool:
        """
        设置单个 cookie

        Args:
            cookie: Cookie 模型对象

        Returns:
            bool: 设置是否成功

        Raises:
            RuntimeError: 当 CDP 调用失败时
        """
        try:
            await self._ensure_initialized()

            # 转换为 CDP 格式
            cdp_cookie = self._convert_cookie_to_cdp_format(cookie)

            # 调用 CDP 设置 cookie
            result = await self.cdp_session.send("Network.setCookie", cdp_cookie)

            if result.get("success", False):
                # 存储到本地缓存
                cookie_key = f"{cookie.domain}:{cookie.name}"
                self.cookies_store[cookie_key] = cdp_cookie
                logger.debug(f"成功设置 cookie: {cookie.name} @ {cookie.domain}")
                return True
            else:
                logger.warning(f"设置 cookie 失败: {cookie.name} @ {cookie.domain}, 原因: {result}")
                return False

        except Exception as e:
            error_msg = f"设置 cookie 失败 ({cookie.name} @ {cookie.domain}): {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def set_cookies_batch(self, cookies: list[CookieModel]) -> dict[str, Any]:
        """
        批量设置 cookies

        Args:
            cookies: Cookie 模型对象列表

        Returns:
            dict: 包含成功和失败统计的结果

        Raises:
            RuntimeError: 当初始化失败时
        """
        await self._ensure_initialized()

        success_count = 0
        failed_count = 0
        failed_cookies = []

        for cookie in cookies:
            try:
                success = await self.set_cookie(cookie)
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    failed_cookies.append(f"{cookie.name}@{cookie.domain}")
            except Exception as e:
                failed_count += 1
                failed_cookies.append(f"{cookie.name}@{cookie.domain}")
                logger.error(f"设置 cookie 异常: {e}")

        result = {
            "total": len(cookies),
            "success": success_count,
            "failed": failed_count,
            "failed_cookies": failed_cookies,
        }

        logger.info(f"批量设置 cookies 完成: {success_count}/{len(cookies)} 成功")
        return result

    async def get_cookies(self, domain: str | None = None) -> list[dict[str, Any]]:
        """
        获取 cookies

        Args:
            domain: 可选的域名过滤

        Returns:
            list: Cookie 列表

        Raises:
            RuntimeError: 当 CDP 调用失败时
        """
        try:
            await self._ensure_initialized()

            # 调用 CDP 获取 cookies
            if domain:
                result = await self.cdp_session.send("Network.getCookies", {"urls": [f"https://{domain.lstrip('.')}"]})
            else:
                result = await self.cdp_session.send("Network.getCookies")

            cookies = result.get("cookies", [])
            logger.debug(f"获取到 {len(cookies)} 个 cookies")
            return cookies

        except Exception as e:
            error_msg = f"获取 cookies 失败: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def delete_cookie(self, name: str, domain: str) -> bool:
        """
        删除 cookie

        Args:
            name: Cookie 名称
            domain: Cookie 域名

        Returns:
            bool: 删除是否成功

        Raises:
            RuntimeError: 当 CDP 调用失败时
        """
        try:
            await self._ensure_initialized()

            # 构建删除参数
            delete_params = {
                "name": name,
                "domain": domain,
            }

            # 调用 CDP 删除 cookie
            await self.cdp_session.send("Network.deleteCookies", delete_params)

            # 从本地缓存中移除
            cookie_key = f"{domain}:{name}"
            if cookie_key in self.cookies_store:
                del self.cookies_store[cookie_key]

            logger.debug(f"成功删除 cookie: {name} @ {domain}")
            return True

        except Exception as e:
            error_msg = f"删除 cookie 失败 ({name} @ {domain}): {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def cleanup_expired_cookies(self) -> int:
        """
        清理过期的 cookies

        Returns:
            int: 清理的 cookie 数量
        """
        try:
            current_time = time.time()
            cleaned_count = 0

            # 获取所有 cookies
            all_cookies = await self.get_cookies()

            for cookie in all_cookies:
                expires = cookie.get("expires")
                if expires and expires < current_time:
                    # Cookie 已过期，删除它
                    await self.delete_cookie(cookie["name"], cookie["domain"])
                    cleaned_count += 1

            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个过期的 cookies")

            return cleaned_count

        except Exception as e:
            logger.error(f"清理过期 cookies 失败: {str(e)}")
            return 0

    async def save_cookies_to_storage(self, file_path: str) -> bool:
        """
        保存 cookies 到文件

        Args:
            file_path: 保存文件路径

        Returns:
            bool: 保存是否成功
        """
        try:
            # 获取所有 cookies
            cookies = await self.get_cookies()

            # 保存到文件
            storage_path = Path(file_path)
            storage_path.parent.mkdir(parents=True, exist_ok=True)

            with open(storage_path, "w", encoding="utf-8") as f:
                json.dump(cookies, f, indent=2, ensure_ascii=False)

            logger.info(f"成功保存 {len(cookies)} 个 cookies 到 {file_path}")
            return True

        except Exception as e:
            logger.error(f"保存 cookies 到文件失败: {str(e)}")
            return False

    async def load_cookies_from_storage(self, file_path: str) -> int:
        """
        从文件加载 cookies

        Args:
            file_path: 文件路径

        Returns:
            int: 加载的 cookie 数量
        """
        try:
            storage_path = Path(file_path)
            if not storage_path.exists():
                logger.warning(f"Cookie 存储文件不存在: {file_path}")
                return 0

            with open(storage_path, encoding="utf-8") as f:
                cookies_data = json.load(f)

            # 转换为 CookieModel 并设置
            loaded_count = 0
            for cookie_data in cookies_data:
                try:
                    # 创建 CookieModel 实例
                    cookie = CookieModel(
                        name=cookie_data["name"],
                        value=cookie_data["value"],
                        domain=cookie_data["domain"],
                        path=cookie_data.get("path", "/"),
                        expires=cookie_data.get("expires"),
                        httpOnly=cookie_data.get("httpOnly", False),
                        secure=cookie_data.get("secure", False),
                        sameSite=cookie_data.get("sameSite", "Lax"),
                    )

                    # 设置 cookie
                    success = await self.set_cookie(cookie)
                    if success:
                        loaded_count += 1

                except Exception as e:
                    logger.warning(f"加载 cookie 失败: {e}")

            logger.info(f"成功从文件加载 {loaded_count} 个 cookies")
            return loaded_count

        except Exception as e:
            logger.error(f"从文件加载 cookies 失败: {str(e)}")
            return 0
