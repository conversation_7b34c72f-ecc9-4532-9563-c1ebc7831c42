"""
脚本相关的 Pydantic 模型

定义脚本执行 API 请求和响应的数据结构
"""

from datetime import datetime

from pydantic import BaseModel, Field, field_validator

from app.config.enums import ExecutionStatus


class ScriptExecuteRequest(BaseModel):
    """脚本执行请求模型"""

    script: str = Field(..., description="要执行的Python脚本内容")
    username: str | None = Field(None, description="可选的用户名")
    password: str | None = Field(None, description="可选的密码")

    @field_validator("script")
    @classmethod
    def validate_script(cls, value: str) -> str:
        """验证脚本内容不能为空"""
        if not value or not value.strip():
            raise ValueError("脚本内容不能为空")
        return value.strip()


class ScriptExecuteResponse(BaseModel):
    """脚本执行响应模型"""

    ticket_id: str = Field(..., description="脚本唯一标识符")
    status: str = Field(..., description="脚本执行状态")
    message: str = Field(..., description="状态描述信息")


class ScriptResultResponse(BaseModel):
    """获取脚本执行结果的响应模型"""

    ticket_id: str = Field(..., description="脚本唯一标识符")
    status: ExecutionStatus = Field(..., description="脚本执行状态")
    script_content: str = Field(..., description="脚本内容")
    stdout: str | None = Field(None, description="标准输出")
    stderr: str | None = Field(None, description="标准错误输出")
    return_code: int | None = Field(None, description="返回码")
    replica_id: str | None = Field(None, description="执行该脚本的副本ID")
    selenium_session_id: str | None = Field(None, description="Selenium会话ID")
    created_at: datetime | None = Field(None, description="创建时间")
    updated_at: datetime | None = Field(None, description="更新时间")
    started_at: datetime | None = Field(None, description="开始执行时间")
    completed_at: datetime | None = Field(None, description="完成时间")


class ScriptStatusResponse(BaseModel):
    """获取脚本状态的响应模型（轻量级）"""

    ticket_id: str = Field(..., description="脚本唯一标识符")
    status: ExecutionStatus = Field(..., description="脚本执行状态")
    replica_id: str | None = Field(None, description="执行该脚本的副本ID")


class ScriptTerminateResponse(BaseModel):
    """终止脚本执行的响应模型"""

    ticket_id: str = Field(..., description="脚本唯一标识符")
    message: str = Field(..., description="操作结果描述")
    success: bool = Field(..., description="是否成功终止")
