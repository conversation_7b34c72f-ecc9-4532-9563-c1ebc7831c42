import argparse
import asyncio
import re

from playwright import async_api
from playwright._impl._errors import TimeoutError

from app.utils.custom_actions import login_ecloud


def parse_arguments():
    parser = argparse.ArgumentParser(description="WebUI测试脚本")
    parser.add_argument("--cdp_url", "-c", required=True, help="CDP地址")
    parser.add_argument("--username", "-u", help="登录用户名")
    parser.add_argument("--password", "-p", help="登录密码")
    return parser.parse_args()


async def run_test():
    # 解析命令行参数
    args = parse_arguments()

    pw = None
    browser = None
    context = None

    try:
        # Start a Playwright session in asynchronous mode
        pw = await async_api.async_playwright().start()

        # Launch a Chromium browser in headless mode with custom arguments
        browser = await pw.chromium.connect_over_cdp(args.cdp_url, slow_mo=1000)

        # Create a new browser context (like an incognito window)
        context = await browser.new_context(viewport={"width": 1920, "height": 1080})
        context.set_default_timeout(5000)

        # Open a new page in the browser context
        page = await context.new_page()

        # 登录步骤（如果提供了用户名和密码）
        if args.username and args.password:
            await login_ecloud(username=args.username, password=args.password, page=page, is_script=True)

        # 打开url：https://ecloud.10086.cn/portal
        await page.goto("https://ecloud.10086.cn/portal", wait_until="commit", timeout=5000)

        # Wait for the main page to reach DOMContentLoaded state (optional for stability)
        try:
            await page.wait_for_load_state("domcontentloaded", timeout=3000)
        except async_api.Error:
            pass

        # Iterate through all iframes and wait for them to load as well
        for frame in page.frames:
            try:
                await frame.wait_for_load_state("domcontentloaded", timeout=3000)
            except async_api.Error:
                pass

        # 点击'产品'按钮
        frame = context.pages[0]
        await frame.wait_for_timeout(3000)
        try:
            await frame.get_by_text(re.compile("产品")).nth(0).click(timeout=5000)
        except TimeoutError:
            await (
                frame.locator("xpath=html/body/div[1]/div[2]/div/div[1]/div[2]/div/div[2]/a").nth(0).click(timeout=5000)
            )

        # 点击'九天人工智能'按钮
        frame = context.pages[0]
        await frame.wait_for_timeout(3000)
        try:
            await frame.get_by_text(re.compile("九天人工智能")).nth(0).click(timeout=5000)
        except TimeoutError:
            await (
                frame.locator("xpath=html/body/div[1]/div[2]/div/div[3]/div[2]/div/div/div[1]/div/ul/li[11]/a")
                .nth(0)
                .click(timeout=5000)
            )

        # 点击'智算平台'按钮
        frame = context.pages[0]
        await frame.wait_for_timeout(3000)
        try:
            await frame.get_by_text(re.compile("智算平台")).nth(0).click(timeout=5000)
        except TimeoutError:
            await (
                frame.locator(
                    "xpath=html/body/div[1]/div[2]/div/div[3]/div[2]/div/div/div[2]/div[2]/div/div[1]/div[2]/div[1]"
                )
                .nth(0)
                .click(timeout=5000)
            )

        # 点击'管理控制台'按钮
        frame = context.pages[0]
        await frame.wait_for_timeout(3000)
        try:
            await frame.get_by_text(re.compile("管理控制台")).nth(0).click(timeout=5000)
        except TimeoutError:
            await (
                frame.locator("xpath=html/body/div[1]/div[4]/div/div/div[2]/div/div[1]/div/div[2]/div[2]/div[2]")
                .nth(0)
                .click(timeout=5000)
            )

        # 点击'模型开发（IDE）'按钮
        frame = context.pages[0]
        await frame.wait_for_timeout(3000)
        try:
            await frame.get_by_text(re.compile("模型开发（IDE）")).nth(0).click(timeout=5000)
        except TimeoutError:
            await (
                frame.locator("xpath=html/body/div[2]/div/div/div[1]/div/div/div[2]/ul/li[8]")
                .nth(0)
                .click(timeout=5000)
            )

        # 点击'帮助文档'按钮
        frame = context.pages[0]
        await frame.wait_for_timeout(3000)
        try:
            await frame.get_by_text(re.compile("帮助文档")).nth(0).click(timeout=5000)
        except TimeoutError:
            await (
                frame.locator(
                    "xpath=html/body/div[2]/div/div/div[2]/div/div/div[1]/div[1]/div[1]/div/div[1]/div[2]/div[1]/span"
                )
                .nth(0)
                .click(timeout=5000)
            )

        # 操作完成
        print(
            "已成功完成所有8个步骤：1)访问移动云官网 2)选择产品菜单 3)选择九天人工智能 4)进入智算平台 5)进入管理控制台 6)选择模型开发(IDE) 7)点击帮助文档按钮 8)确认使用指南弹窗已正确展示。弹窗内容包含详细的智算平台操作指南。"
        )

        await frame.screenshot(path="screenshot.png")

        await asyncio.sleep(5)

    finally:
        if context:
            try:
                await context.close()
            except Exception:
                pass
        if browser:
            try:
                await browser.close()
            except Exception:
                pass
        if pw:
            try:
                await pw.stop()
            except Exception:
                pass


asyncio.run(run_test())
