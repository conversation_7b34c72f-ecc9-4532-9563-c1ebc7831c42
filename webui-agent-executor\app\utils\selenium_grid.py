"""
Selenium Grid CDP工具方法

用于向Selenium Grid Hub发起请求并获取CDP WebSocket URL
基于Playwright的Selenium Grid实现进行优化
"""

import time
from urllib.parse import urlparse, urlunparse

import httpx

from app.utils.log import logger


class SeleniumGridError(Exception):
    """Selenium Grid连接异常"""

    pass


def get_selenium_cdp_url(hub_url: str, browser_name: str = "chrome", max_retries: int = 3) -> dict:
    """
    连接到Selenium Grid Hub并获取CDP WebSocket URL

    Args:
        hub_url: Selenium Grid Hub地址
        browser_name: 浏览器名称，支持'chrome'或'MicrosoftEdge'
        max_retries: 最大重试次数

    Returns:
        dict: 包含cdp_url和session_id的字典

    Raises:
        SeleniumGridError: 连接失败或响应异常时抛出
    """
    # 标准化Hub URL
    if not hub_url.endswith("/"):
        hub_url += "/"

    session_url = hub_url + "session"

    # 根据浏览器类型设置capabilities
    is_edge = browser_name.lower() in ("microsoftedge", "edge")
    options_key = "ms:edgeOptions" if is_edge else "goog:chromeOptions"
    browser_name_normalized = "MicrosoftEdge" if is_edge else "chrome"

    # 构建请求载荷，参考Playwright的实现
    desired_capabilities = {
        "browserName": browser_name_normalized,
        options_key: {"args": ["--remote-debugging-port=0"]},
    }

    payload = {"capabilities": {"alwaysMatch": desired_capabilities}}

    headers = {"Content-Type": "application/json; charset=utf-8"}
    last_exception = None

    logger.info(f"连接到Selenium Grid: {hub_url}")

    for attempt in range(1, max_retries + 1):
        try:
            logger.debug(f"[第{attempt}次] 向Selenium Grid发起会话请求")
            resp = httpx.post(session_url, json=payload, headers=headers, timeout=30)
            resp.raise_for_status()
            data = resp.json()
            break
        except httpx.HTTPStatusError as e:
            error_msg = f"Selenium Grid HTTP错误 {e.response.status_code}: {e.response.text}"
            logger.error(f"[第{attempt}次] {error_msg}")
            last_exception = SeleniumGridError(error_msg)
        except httpx.TimeoutException as e:
            error_msg = f"连接Selenium Grid超时: {str(e)}"
            logger.error(f"[第{attempt}次] {error_msg}")
            last_exception = SeleniumGridError(error_msg)
        except Exception as e:
            error_msg = f"连接Selenium Grid异常: {str(e)}"
            logger.error(f"[第{attempt}次] {error_msg}")
            last_exception = SeleniumGridError(error_msg)

        if attempt < max_retries:
            wait_time = 2 * attempt  # 递增等待时间
            logger.info(f"等待{wait_time}秒后重试...")
            time.sleep(wait_time)
    else:
        raise SeleniumGridError(f"连接Selenium Grid失败，重试{max_retries}次后仍未成功: {last_exception}")

    # 解析响应数据
    value = data.get("value")
    if not value:
        error_msg = f"Selenium Grid响应格式异常，缺少value字段: {data}"
        logger.error(error_msg)
        raise SeleniumGridError(error_msg)

    session_id = value.get("sessionId")
    capabilities = value.get("capabilities", {})

    if not session_id:
        error_msg = f"Selenium Grid响应缺少sessionId: {data}"
        logger.error(error_msg)
        raise SeleniumGridError(error_msg)

    logger.info(f"成功创建Selenium会话，sessionId={session_id}")

    # 获取CDP URL（仅支持Selenium 4的se:cdp字段）
    cdp_url = capabilities.get("se:cdp")
    if not cdp_url:
        error_msg = f"未能获取CDP地址，capabilities中缺少se:cdp字段: {capabilities}"
        logger.error(error_msg)
        raise SeleniumGridError(error_msg)

    logger.info(f"获取到CDP地址: {cdp_url}")

    # 替换CDP URL中的主机地址为Hub地址
    cdp_url = _normalize_cdp_url(cdp_url, hub_url)

    return {"cdp_url": cdp_url, "session_id": session_id}


def _normalize_cdp_url(cdp_url: str, hub_url: str) -> str:
    """
    标准化CDP URL，将主机地址替换为Hub地址
    参考Playwright的实现逻辑

    Args:
        cdp_url: 原始CDP URL
        hub_url: Selenium Grid Hub URL

    Returns:
        str: 标准化后的CDP URL
    """
    try:
        cdp_parts = urlparse(cdp_url)
        hub_parts = urlparse(hub_url)

        # 如果CDP URL的主机是localhost或127.0.0.1，则替换为Hub的主机
        if cdp_parts.hostname in ("localhost", "127.0.0.1"):
            new_netloc = hub_parts.hostname or ""
            if hub_parts.port:
                new_netloc += f":{hub_parts.port}"
            new_url = urlunparse(
                (cdp_parts.scheme, new_netloc, cdp_parts.path, cdp_parts.params, cdp_parts.query, cdp_parts.fragment)
            )
            logger.info(f"CDP地址已标准化: {cdp_url} -> {new_url}")
            return new_url
        else:
            # 如果不是localhost，直接返回原URL
            logger.debug(f"CDP地址无需标准化: {cdp_url}")
            return cdp_url
    except Exception as e:
        logger.warning(f"CDP地址标准化失败，返回原始地址: {cdp_url}, 错误: {e}")
        return cdp_url


def disconnect_from_selenium(hub_url: str, session_id: str) -> bool:
    """
    关闭Selenium Grid上的session

    Args:
        hub_url: Selenium Grid Hub地址
        session_id: 需要关闭的sessionId

    Returns:
        bool: 是否关闭成功
    """
    if not session_id:
        logger.warning("session_id为空，跳过关闭操作")
        return False

    if not hub_url.endswith("/"):
        hub_url += "/"

    url = f"{hub_url}session/{session_id}"

    try:
        logger.info(f"断开Selenium Grid连接，sessionId={session_id}")
        resp = httpx.delete(url, timeout=10)

        if resp.status_code in (200, 404):
            # 200表示成功关闭，404表示session已经不存在（也算成功）
            logger.info(f"已断开Selenium Grid连接，sessionId={session_id}")
            return True
        else:
            logger.warning(f"断开Selenium Grid连接失败，状态码: {resp.status_code}, 响应: {resp.text}")
            return False

    except httpx.TimeoutException:
        logger.error(f"断开Selenium Grid连接超时: {session_id}")
        return False
    except Exception as e:
        logger.error(f"断开Selenium Grid连接异常: {session_id}, 错误: {e}")
        return False
