#!/usr/bin/env python3
"""
数据完整性监控工具
定期检查关键字段的完整性，并提供自动修复功能
"""

import asyncio
import json
from datetime import datetime, timedelta

from loguru import logger

from app.config.settings import settings
from app.services.mq_consumer import DatabaseOperator
from app.utils.minio_storage import get_minio_storage


class DataIntegrityMonitor:
    """数据完整性监控器"""

    def __init__(self):
        self.db_operator = DatabaseOperator()
        self.minio_storage = None
        if settings.minio_enabled:
            self.minio_storage = get_minio_storage()

    async def run_integrity_check(self, hours_back: int = 24) -> dict:
        """运行数据完整性检查"""
        logger.info(f"开始数据完整性检查 (检查过去 {hours_back} 小时的任务)")

        if not self.db_operator.connect():
            logger.error("数据库连接失败")
            return {"success": False, "error": "数据库连接失败"}

        try:
            # 获取需要检查的任务
            tasks = self._get_tasks_to_check(hours_back)
            logger.info(f"找到 {len(tasks)} 个任务需要检查")

            # 执行各项检查
            results = {
                "total_tasks": len(tasks),
                "started_at_issues": [],
                "selenium_session_issues": [],
                "file_path_issues": [],
                "summary": {},
            }

            for task in tasks:
                await self._check_task_integrity(task, results)

            # 生成摘要
            results["summary"] = {
                "started_at_missing": len(results["started_at_issues"]),
                "selenium_session_missing": len(results["selenium_session_issues"]),
                "file_path_inconsistent": len(results["file_path_issues"]),
                "total_issues": len(results["started_at_issues"])
                + len(results["selenium_session_issues"])
                + len(results["file_path_issues"]),
            }

            logger.info(f"数据完整性检查完成: {results['summary']}")
            return {"success": True, "results": results}

        finally:
            self.db_operator.close()

    def _get_tasks_to_check(self, hours_back: int) -> list[dict]:
        """获取需要检查的任务"""
        cutoff_time = datetime.now() - timedelta(hours=hours_back)

        sql = """
            SELECT ticket_id, status, created_at, updated_at, started_at, completed_at,
                   selenium_session_id, screenshot_file_paths, video_file_paths, download_file_paths
            FROM task
            WHERE created_at >= %s
            ORDER BY created_at DESC;
        """

        with self.db_operator.connection.cursor() as cursor:
            cursor.execute(sql, (cutoff_time,))
            results = cursor.fetchall()
            return [dict(row) for row in results]

    async def _check_task_integrity(self, task: dict, results: dict):
        """检查单个任务的数据完整性"""
        ticket_id = task["ticket_id"]
        status = task["status"]

        # 检查1: started_at字段
        if status in ["RUNNING", "SUCCESS", "FAILED", "CANCELLED"] and not task["started_at"]:
            issue = {
                "ticket_id": ticket_id,
                "status": status,
                "created_at": str(task["created_at"]),
                "issue": "started_at字段缺失",
            }
            results["started_at_issues"].append(issue)
            logger.warning(f"发现started_at缺失: {ticket_id}")

        # 检查2: selenium_session_id字段
        if status in ["RUNNING", "SUCCESS", "FAILED", "CANCELLED"] and not task["selenium_session_id"]:
            # 检查是否有相关的日志记录表明应该有session_id
            if await self._should_have_selenium_session(ticket_id):
                issue = {
                    "ticket_id": ticket_id,
                    "status": status,
                    "created_at": str(task["created_at"]),
                    "issue": "selenium_session_id字段缺失",
                }
                results["selenium_session_issues"].append(issue)
                logger.warning(f"发现selenium_session_id缺失: {ticket_id}")

        # 检查3: 文件路径一致性
        if settings.minio_enabled and self.minio_storage:
            await self._check_file_path_consistency(task, results)

    async def _should_have_selenium_session(self, ticket_id: str) -> bool:
        """检查任务是否应该有selenium会话ID"""
        # 这里可以检查日志文件或其他指标
        # 简化版本：如果任务运行时间超过30秒，认为应该有session_id
        return True  # 简化实现

    async def _check_file_path_consistency(self, task: dict, results: dict):
        """检查文件路径一致性"""
        ticket_id = task["ticket_id"]

        # 获取数据库中的文件路径
        db_screenshot_paths = task.get("screenshot_file_paths") or []
        db_video_paths = task.get("video_file_paths") or []
        db_download_paths = task.get("download_file_paths") or []

        # 扫描MinIO中的实际文件
        actual_screenshot_paths = await self._scan_minio_files("screenshot", ticket_id)
        actual_video_paths = await self._scan_minio_files("video", ticket_id)
        actual_download_paths = await self._scan_minio_files("file", ticket_id)

        # 比较数量
        if (
            len(db_screenshot_paths) != len(actual_screenshot_paths)
            or len(db_video_paths) != len(actual_video_paths)
            or len(db_download_paths) != len(actual_download_paths)
        ):
            issue = {
                "ticket_id": ticket_id,
                "status": task["status"],
                "db_files": {
                    "screenshots": len(db_screenshot_paths),
                    "videos": len(db_video_paths),
                    "downloads": len(db_download_paths),
                },
                "actual_files": {
                    "screenshots": len(actual_screenshot_paths),
                    "videos": len(actual_video_paths),
                    "downloads": len(actual_download_paths),
                },
                "issue": "文件路径数量不一致",
            }
            results["file_path_issues"].append(issue)
            logger.warning(f"发现文件路径不一致: {ticket_id}")

    async def _scan_minio_files(self, file_type: str, ticket_id: str) -> list[str]:
        """扫描MinIO中的文件"""
        if not self.minio_storage:
            return []

        try:
            prefix = f"{file_type}/{ticket_id}/"
            objects = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: list(
                    self.minio_storage.client.list_objects(
                        bucket_name=self.minio_storage.bucket_name, prefix=prefix, recursive=True
                    )
                ),
            )

            return [obj.object_name for obj in objects if obj.object_name]
        except Exception as e:
            logger.warning(f"扫描MinIO文件失败: {file_type}/{ticket_id}, 错误: {e}")
            return []

    async def auto_fix_issues(self, check_results: dict) -> dict:
        """自动修复发现的问题"""
        logger.info("开始自动修复数据完整性问题")

        if not self.db_operator.connect():
            logger.error("数据库连接失败")
            return {"success": False, "error": "数据库连接失败"}

        try:
            fix_results = {"started_at_fixed": 0, "selenium_session_fixed": 0, "file_paths_fixed": 0, "errors": []}

            # 修复started_at问题
            for issue in check_results["results"]["started_at_issues"]:
                if await self._fix_started_at_issue(issue):
                    fix_results["started_at_fixed"] += 1
                else:
                    fix_results["errors"].append(f"修复started_at失败: {issue['ticket_id']}")

            # 修复文件路径问题
            for issue in check_results["results"]["file_path_issues"]:
                if await self._fix_file_path_issue(issue):
                    fix_results["file_paths_fixed"] += 1
                else:
                    fix_results["errors"].append(f"修复文件路径失败: {issue['ticket_id']}")

            logger.info(f"自动修复完成: {fix_results}")
            return {"success": True, "results": fix_results}

        finally:
            self.db_operator.close()

    async def _fix_started_at_issue(self, issue: dict) -> bool:
        """修复started_at问题"""
        try:
            ticket_id = issue["ticket_id"]
            # 使用创建时间作为开始时间的估算

            sql = "UPDATE task SET started_at = created_at WHERE ticket_id = %s AND started_at IS NULL"
            self.db_operator.execute_query(sql, (ticket_id,))

            logger.info(f"修复started_at成功: {ticket_id}")
            return True
        except Exception as e:
            logger.error(f"修复started_at失败: {issue['ticket_id']}, 错误: {e}")
            return False

    async def _fix_file_path_issue(self, issue: dict) -> bool:
        """修复文件路径问题"""
        try:
            ticket_id = issue["ticket_id"]

            # 重新扫描并更新文件路径
            screenshot_paths = await self._scan_minio_files("screenshot", ticket_id)
            video_paths = await self._scan_minio_files("video", ticket_id)
            download_paths = await self._scan_minio_files("file", ticket_id)

            sql = """
                UPDATE task SET
                    screenshot_file_paths = %s,
                    video_file_paths = %s,
                    download_file_paths = %s,
                    updated_at = NOW()
                WHERE ticket_id = %s
            """

            self.db_operator.execute_query(
                sql,
                (
                    json.dumps(screenshot_paths) if screenshot_paths else None,
                    json.dumps(video_paths) if video_paths else None,
                    json.dumps(download_paths) if download_paths else None,
                    ticket_id,
                ),
            )

            logger.info(f"修复文件路径成功: {ticket_id}")
            return True
        except Exception as e:
            logger.error(f"修复文件路径失败: {issue['ticket_id']}, 错误: {e}")
            return False


async def main():
    """主函数 - 用于独立运行检查"""
    monitor = DataIntegrityMonitor()

    # 运行完整性检查
    check_results = await monitor.run_integrity_check(hours_back=24)

    if check_results["success"]:
        print(f"检查完成: {check_results['results']['summary']}")

        # 如果发现问题，询问是否自动修复
        total_issues = check_results["results"]["summary"]["total_issues"]
        if total_issues > 0:
            print(f"发现 {total_issues} 个问题")
            fix_confirm = input("是否自动修复这些问题? (y/N): ")
            if fix_confirm.lower() == "y":
                fix_results = await monitor.auto_fix_issues(check_results)
                print(f"修复结果: {fix_results}")
        else:
            print("未发现数据完整性问题")
    else:
        print(f"检查失败: {check_results['error']}")


if __name__ == "__main__":
    asyncio.run(main())
