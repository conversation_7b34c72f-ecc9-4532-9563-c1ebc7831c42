"""
RabbitMQ消息消费者服务

重构版本：采用同步数据库操作 + 线程池的架构，避免复杂的asyncio+threading组合
"""

import json
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from concurrent.futures import TimeoutError as FutureTimeoutError
from datetime import datetime, timedelta
from typing import Any

import pika
import psycopg2
from pika.adapters.blocking_connection import BlockingChannel
from pika.exceptions import AMQPChannelError, AMQPConnectionError
from psycopg2.extras import RealDictCursor

from app.config.enums import ExecutionStatus
from app.config.settings import settings
from app.schemas.mq_message import (
    AppendLogMessage,
    DatabaseMessage,
    DatabaseOperationType,
    UpdateErrorMessage,
    UpdateResultMessage,
    UpdateSeleniumSessionMessage,
    UpdateStatusMessage,
)
from app.services.callback_service import get_callback_service
from app.utils.log import logger
from app.utils.timezone import get_shanghai_now


class DatabaseOperator:
    """同步数据库操作类，专门用于消费者线程"""

    def __init__(self):
        self.connection: psycopg2.extensions.connection | None = None
        self._lock = threading.Lock()

    def connect(self) -> bool:
        """建立数据库连接"""
        try:
            with self._lock:
                if self.connection and not self.connection.closed:
                    return True

                # 解析数据库URL
                db_url = settings.database_url
                if not db_url or db_url == "":
                    logger.error("数据库URL未配置")
                    return False

                # 解析数据库URL格式：postgres://user:password@host:port/database
                if db_url.startswith("postgres://"):
                    # 去掉协议前缀
                    url_part = db_url[11:]  # 去掉 postgres://

                    if "@" in url_part:
                        auth_part, host_part = url_part.split("@", 1)
                        if ":" in auth_part:
                            username, password = auth_part.split(":", 1)
                        else:
                            username = auth_part
                            password = ""

                        if "/" in host_part:
                            host_port, database = host_part.split("/", 1)
                        else:
                            host_port = host_part
                            database = "postgres"

                        if ":" in host_port:
                            host, port = host_port.split(":", 1)
                            port = int(port)
                        else:
                            host = host_port
                            port = 5432  # PostgreSQL默认端口
                    else:
                        logger.error(f"无效的数据库URL格式: {db_url}")
                        return False
                else:
                    logger.error(f"不支持的数据库URL格式，请使用postgres://格式: {db_url}")
                    return False

                self.connection = psycopg2.connect(
                    host=host,
                    port=port,
                    user=username,
                    password=password,
                    database=database,
                    cursor_factory=RealDictCursor,
                    connect_timeout=10,
                )

                # PostgreSQL需要单独设置autocommit
                self.connection.autocommit = True

                logger.info(f"数据库连接成功: {host}:{port}/{database}")
                return True

        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False

    def close(self):
        """关闭数据库连接"""
        try:
            with self._lock:
                if self.connection:
                    self.connection.close()
                    self.connection = None
                    logger.debug("数据库连接已关闭")
        except Exception as e:
            logger.debug(f"关闭数据库连接时发生错误: {str(e)}")

    def execute_query(self, sql: str, params: tuple | None = None, fetch: bool = False) -> Any:
        """执行SQL查询"""
        try:
            with self._lock:
                if not self.connection or self.connection.closed:
                    if not self.connect():
                        raise Exception("无法建立数据库连接")

                assert self.connection is not None  # 类型检查断言
                with self.connection.cursor() as cursor:
                    cursor.execute(sql, params or ())

                    if fetch:
                        return cursor.fetchall()
                    else:
                        return cursor.rowcount

        except Exception as e:
            logger.error(f"数据库操作失败: SQL={sql}, 错误={str(e)}")
            # 连接可能已断开，下次重新连接
            self.connection = None
            raise

    def update_task_status(self, ticket_id: str, status: ExecutionStatus, **kwargs) -> bool:
        """更新任务状态"""
        try:
            set_clauses = ["status = %s", "updated_at = %s"]
            params = [status.value, get_shanghai_now()]

            # 处理额外的字段更新，确保序列化字典类型
            for field, value in kwargs.items():
                if field in ["result", "error_message", "step_log", "replica_id", "started_at", "completed_at"]:
                    set_clauses.append(f"{field} = %s")

                    # 如果是字典或列表，转换为JSON字符串
                    if isinstance(value, dict | list):
                        value = json.dumps(value, ensure_ascii=False)

                    params.append(value)

            sql = f"UPDATE task SET {', '.join(set_clauses)} WHERE ticket_id = %s"
            params.append(ticket_id)

            # 调试日志
            logger.debug(f"SQL: {sql}")
            logger.debug(f"参数: {params}")

            rowcount = self.execute_query(sql, tuple(params))

            if rowcount == 0:
                logger.warning(f"任务不存在或已被删除: {ticket_id}")
                return True  # 认为成功，避免重试
            else:
                logger.info(f"更新任务状态成功: {ticket_id} -> {status}")
                return True

        except Exception as e:
            logger.error(f"更新任务状态失败: {ticket_id}, 错误: {str(e)}")
            return False

    def update_selenium_session(self, ticket_id: str, session_id: str) -> bool:
        """更新Selenium会话"""
        try:
            sql = "UPDATE task SET selenium_session_id = %s, updated_at = %s WHERE ticket_id = %s"
            self.execute_query(sql, (session_id, get_shanghai_now(), ticket_id))
            return True
        except Exception as e:
            logger.error(f"更新Selenium会话失败: {ticket_id}, 错误: {str(e)}")
            return False

    def append_task_log(self, ticket_id: str, message: str, append: bool = True) -> bool:
        """追加任务日志"""
        try:
            timestamp = get_shanghai_now().strftime("%Y-%m-%d %H:%M:%S")
            formatted_message = f"{timestamp}：{message}"

            if append:
                # 查询当前日志
                current_log_sql = "SELECT step_log FROM task WHERE ticket_id = %s"
                result = self.execute_query(current_log_sql, (ticket_id,), fetch=True)

                if result:
                    current_log = result[0].get("step_log", "") or ""
                    updated_log = f"{current_log}\n{formatted_message}" if current_log else formatted_message
                else:
                    updated_log = formatted_message
            else:
                updated_log = formatted_message

            # 更新日志
            update_sql = "UPDATE task SET step_log = %s, updated_at = %s WHERE ticket_id = %s"
            self.execute_query(update_sql, (updated_log, get_shanghai_now(), ticket_id))

            logger.debug(f"更新步骤日志成功: {ticket_id}")
            return True

        except Exception as e:
            logger.error(f"更新步骤日志失败: {ticket_id}, 错误: {str(e)}")
            return False

    def update_task_result(self, ticket_id: str, result: Any) -> bool:
        """更新任务结果"""
        try:
            # 如果结果是字典或列表，转换为JSON字符串
            if isinstance(result, dict | list):
                result = json.dumps(result, ensure_ascii=False)

            sql = "UPDATE task SET result = %s, updated_at = %s WHERE ticket_id = %s"
            self.execute_query(sql, (result, get_shanghai_now(), ticket_id))

            logger.debug(f"更新任务结果成功: {ticket_id}")
            return True

        except Exception as e:
            logger.error(f"更新任务结果失败: {ticket_id}, 错误: {str(e)}")
            return False

    def update_task_error(self, ticket_id: str, error_message: str) -> bool:
        """更新任务错误"""
        try:
            sql = "UPDATE task SET error_message = %s, status = %s, updated_at = %s WHERE ticket_id = %s"
            self.execute_query(sql, (error_message, ExecutionStatus.FAILED.value, get_shanghai_now(), ticket_id))

            logger.debug(f"更新任务错误成功: {ticket_id}")
            return True

        except Exception as e:
            logger.error(f"更新任务错误失败: {ticket_id}, 错误: {str(e)}")
            return False

    def update_task_file_paths(
        self,
        ticket_id: str,
        video_file_paths: list[str] | None,
        screenshot_file_paths: list[str] | None,
        download_file_paths: list[str] | None,
    ) -> bool:
        """更新任务文件路径"""
        try:
            import json

            # 将列表转换为JSON字符串
            video_paths_json = json.dumps(video_file_paths) if video_file_paths else None
            screenshot_paths_json = json.dumps(screenshot_file_paths) if screenshot_file_paths else None
            download_paths_json = json.dumps(download_file_paths) if download_file_paths else None

            sql = """
                UPDATE task SET
                    video_file_paths = %s,
                    screenshot_file_paths = %s,
                    download_file_paths = %s,
                    updated_at = %s
                WHERE ticket_id = %s
            """

            self.execute_query(
                sql,
                (
                    video_paths_json,
                    screenshot_paths_json,
                    download_paths_json,
                    get_shanghai_now(),
                    ticket_id,
                ),
            )

            logger.debug(f"更新任务文件路径成功: {ticket_id}")
            return True

        except Exception as e:
            logger.error(f"更新任务文件路径失败: {ticket_id}, 错误: {str(e)}")
            return False

    def append_screenshot_path(self, ticket_id: str, screenshot_path: str) -> bool:
        """追加截图路径到数据库（原子性操作）"""
        try:
            # 使用PostgreSQL的JSON操作来原子性地追加截图路径
            # 如果screenshot_file_paths为NULL，则创建新数组；否则追加到现有数组
            sql = """
                UPDATE task SET
                    screenshot_file_paths = CASE
                        WHEN screenshot_file_paths IS NULL THEN %s::jsonb
                        ELSE screenshot_file_paths || %s::jsonb
                    END,
                    updated_at = %s
                WHERE ticket_id = %s
            """

            import json

            # 创建包含新截图路径的JSON数组
            new_path_json = json.dumps([screenshot_path])

            self.execute_query(
                sql,
                (
                    new_path_json,  # 用于NULL情况的新数组
                    new_path_json,  # 用于追加的数组
                    get_shanghai_now(),
                    ticket_id,
                ),
            )

            logger.info(f"追加截图路径成功: {ticket_id} -> {screenshot_path}")
            return True

        except Exception as e:
            logger.error(f"追加截图路径失败: {ticket_id}, 错误: {str(e)}")
            return False

    def append_video_path(self, ticket_id: str, video_path: str) -> bool:
        """追加视频路径到数据库（原子性操作）"""
        try:
            # 使用PostgreSQL的JSON操作来原子性地追加视频路径
            # 如果video_file_paths为NULL，则创建新数组；否则追加到现有数组
            sql = """
                UPDATE task SET
                    video_file_paths = CASE
                        WHEN video_file_paths IS NULL THEN %s::jsonb
                        ELSE video_file_paths || %s::jsonb
                    END,
                    updated_at = %s
                WHERE ticket_id = %s
            """

            import json

            # 创建包含新视频路径的JSON数组
            new_path_json = json.dumps([video_path])

            self.execute_query(
                sql,
                (
                    new_path_json,  # 用于NULL情况的新数组
                    new_path_json,  # 用于追加的数组
                    get_shanghai_now(),
                    ticket_id,
                ),
            )

            logger.info(f"追加视频路径成功: {ticket_id} -> {video_path}")
            return True

        except Exception as e:
            logger.error(f"追加视频路径失败: {ticket_id}, 错误: {str(e)}")
            return False

    def append_file_path(self, ticket_id: str, file_path: str) -> bool:
        """追加下载文件路径到数据库（原子性操作）"""
        try:
            # 使用PostgreSQL的JSON操作来原子性地追加下载文件路径
            # 如果download_file_paths为NULL，则创建新数组；否则追加到现有数组
            sql = """
                UPDATE task SET
                    download_file_paths = CASE
                        WHEN download_file_paths IS NULL THEN %s::jsonb
                        ELSE download_file_paths || %s::jsonb
                    END,
                    updated_at = %s
                WHERE ticket_id = %s
            """

            import json

            # 创建包含新下载文件路径的JSON数组
            new_path_json = json.dumps([file_path])

            self.execute_query(
                sql,
                (
                    new_path_json,  # 用于NULL情况的新数组
                    new_path_json,  # 用于追加的数组
                    get_shanghai_now(),
                    ticket_id,
                ),
            )

            logger.info(f"追加下载文件路径成功: {ticket_id} -> {file_path}")
            return True

        except Exception as e:
            logger.error(f"追加下载文件路径失败: {ticket_id}, 错误: {str(e)}")
            return False


class DatabaseMessageConsumer:
    """数据库操作消息消费者 - 重构版本"""

    def __init__(self):
        self.connection: pika.BlockingConnection | None = None
        self.channel: BlockingChannel | None = None
        self._running = False
        self._consumer_thread: threading.Thread | None = None
        self._processed_messages: set[str] = set()  # 幂等性控制
        self._message_timestamps: dict[str, datetime] = {}  # 消息时间戳
        self._executor: ThreadPoolExecutor | None = None
        self._db_operator: DatabaseOperator | None = None

        # 统计信息
        self._stats = {
            "total_messages": 0,
            "successful_messages": 0,
            "failed_messages": 0,
            "timeout_messages": 0,
            "dead_letter_messages": 0,
            "retry_messages": 0,
            "processing_times": [],  # 存储最近100个处理时间
        }
        self._stats_lock = threading.Lock()

    def start_consumer(self):
        """启动消费者"""
        if self._running:
            logger.warning("消费者已经在运行中")
            return

        try:
            logger.info("🚀 正在启动RabbitMQ消费者...")

            # 创建线程池和数据库操作器
            logger.info("📦 创建线程池和数据库操作器...")
            self._executor = ThreadPoolExecutor(max_workers=8, thread_name_prefix="db_worker")
            self._db_operator = DatabaseOperator()

            # 启动消费者线程
            logger.info("🔄 启动消费者线程...")
            self._consumer_thread = threading.Thread(
                target=self._consumer_main_loop, daemon=True, name="rabbitmq_consumer"
            )
            self._consumer_thread.start()

            self._running = True
            logger.info(f"✅ RabbitMQ消费者启动成功: {settings.rabbitmq_host}:{settings.rabbitmq_port}")

        except Exception as e:
            import traceback

            logger.error(f"❌ 启动RabbitMQ消费者失败: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            self._cleanup()
            raise

    def _consumer_main_loop(self):
        """消费者主循环"""
        retry_count = 0
        max_retries = 3

        logger.info("🔄 进入消费者主循环...")

        while self._running and retry_count < max_retries:
            try:
                logger.info(f"🌐 尝试建立RabbitMQ连接 (尝试 {retry_count + 1}/{max_retries + 1})...")

                # 建立RabbitMQ连接
                if not self._connect_rabbitmq():
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(f"⚠️ RabbitMQ连接失败，{5}秒后重试... ({retry_count}/{max_retries})")
                        time.sleep(5)
                        continue
                    else:
                        logger.error("❌ RabbitMQ连接重试次数已达上限，停止消费者")
                        break

                logger.info("✅ RabbitMQ连接成功，尝试建立数据库连接...")

                # 建立数据库连接
                assert self._db_operator is not None  # 类型检查断言
                if not self._db_operator.connect():
                    logger.error("❌ 数据库连接失败，停止消费者")
                    break

                retry_count = 0  # 重置重试计数
                logger.info("✅ 数据库连接成功，开始消费消息...")

                # 启动清理任务
                self._start_cleanup_task()

                # 消费循环
                while self._running:
                    try:
                        assert self.connection is not None  # 类型检查断言
                        self.connection.process_data_events(time_limit=1)

                        # 检查连接状态
                        if self.connection.is_closed:
                            logger.warning("⚠️ RabbitMQ连接已关闭，尝试重连...")
                            break

                    except (AMQPConnectionError, AMQPChannelError) as e:
                        if self._running:
                            logger.error(f"❌ RabbitMQ连接错误: {str(e)}")
                            break
                    except Exception as e:
                        if self._running:
                            logger.error(f"❌ 消费消息时发生错误: {str(e)}")
                            time.sleep(1)

            except Exception as e:
                if self._running:
                    import traceback

                    logger.error(f"❌ 消费者主循环异常: {str(e)}")
                    logger.error(f"错误堆栈: {traceback.format_exc()}")
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.info("🔄 5秒后重试...")
                        time.sleep(5)

        logger.info("🛑 消费者主循环结束")
        self._cleanup()

    def _connect_rabbitmq(self) -> bool:
        """建立RabbitMQ连接"""
        try:
            # 创建连接参数
            credentials = pika.PlainCredentials(settings.rabbitmq_username, settings.rabbitmq_password)
            parameters = pika.ConnectionParameters(
                host=settings.rabbitmq_host,
                port=settings.rabbitmq_port,
                virtual_host=settings.rabbitmq_vhost,
                credentials=credentials,
                heartbeat=settings.rabbitmq_heartbeat,
                connection_attempts=1,
                retry_delay=2,
            )

            # 建立连接
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()

            # 声明死信交换机和队列
            self.channel.exchange_declare(
                exchange=settings.rabbitmq_dead_letter_exchange,
                exchange_type="direct",
                durable=settings.rabbitmq_durable,
            )

            self.channel.queue_declare(queue=settings.rabbitmq_dead_letter_queue, durable=settings.rabbitmq_durable)

            self.channel.queue_bind(
                exchange=settings.rabbitmq_dead_letter_exchange,
                queue=settings.rabbitmq_dead_letter_queue,
                routing_key=settings.rabbitmq_dead_letter_routing_key,
            )

            # 声明主交换机和队列
            self.channel.exchange_declare(
                exchange=settings.rabbitmq_db_exchange, exchange_type="direct", durable=settings.rabbitmq_durable
            )

            self.channel.queue_declare(queue=settings.rabbitmq_db_queue, durable=settings.rabbitmq_durable)

            # 绑定队列到交换机
            self.channel.queue_bind(
                exchange=settings.rabbitmq_db_exchange,
                queue=settings.rabbitmq_db_queue,
                routing_key=settings.rabbitmq_routing_key,
            )

            # 设置消费者预取数量和回调
            self.channel.basic_qos(prefetch_count=1)
            self.channel.basic_consume(
                queue=settings.rabbitmq_db_queue, on_message_callback=self._message_callback, auto_ack=False
            )

            logger.debug("RabbitMQ连接和配置完成")
            return True

        except Exception as e:
            logger.error(f"建立RabbitMQ连接失败: {str(e)}")
            return False

    def _message_callback(self, channel, method, properties, body):
        """消息回调处理函数"""
        if not self._running:
            logger.debug("消费者已停止，忽略消息")
            return

        try:
            logger.info("📨 收到新消息，开始处理...")

            # 解析消息
            message_body = body.decode("utf-8")
            message_data = json.loads(message_body)
            db_message = DatabaseMessage(**message_data)

            logger.info(
                f"📋 消息详情: ID={db_message.message_id}, 操作={db_message.operation_type.value}, 任务={db_message.ticket_id}, 重试次数={db_message.retry_count}/{db_message.max_retry}"
            )

            # 幂等性检查
            if db_message.message_id in self._processed_messages:
                logger.debug(f"♻️ 消息已处理，跳过: {db_message.message_id}")
                channel.basic_ack(delivery_tag=method.delivery_tag)
                return

            # 记录消息时间戳和统计
            start_time = datetime.now()
            self._message_timestamps[db_message.message_id] = start_time

            with self._stats_lock:
                self._stats["total_messages"] += 1

            # 提交到线程池处理
            try:
                logger.info(f"⚡ 提交消息到线程池处理: {db_message.message_id}")
                future = self._executor.submit(self._process_message, db_message)
                success = future.result(timeout=settings.rabbitmq_message_timeout)

                # 计算处理时间
                processing_time = (datetime.now() - start_time).total_seconds()

                if success:
                    # 标记消息已处理
                    self._processed_messages.add(db_message.message_id)
                    channel.basic_ack(delivery_tag=method.delivery_tag)
                    logger.info(f"✅ 消息处理成功: {db_message.message_id} (耗时: {processing_time:.2f}秒)")

                    # 更新统计
                    with self._stats_lock:
                        self._stats["successful_messages"] += 1
                        self._stats["processing_times"].append(processing_time)
                        # 只保留最近100个处理时间
                        if len(self._stats["processing_times"]) > 100:
                            self._stats["processing_times"].pop(0)
                else:
                    # 处理失败，检查重试次数
                    with self._stats_lock:
                        self._stats["failed_messages"] += 1
                    self._handle_message_failure(channel, method, db_message, "处理失败")

            except FutureTimeoutError:
                logger.error(
                    f"⏰ 消息处理超时: {db_message.message_id} (超时时间: {settings.rabbitmq_message_timeout}秒)"
                )
                with self._stats_lock:
                    self._stats["timeout_messages"] += 1
                self._handle_message_failure(channel, method, db_message, "处理超时")
            except Exception as e:
                logger.error(f"❌ 消息处理异常: {db_message.message_id}, 错误: {str(e)}")
                with self._stats_lock:
                    self._stats["failed_messages"] += 1
                self._handle_message_failure(channel, method, db_message, f"处理异常: {str(e)}")

        except Exception as e:
            import traceback

            logger.error(f"❌ 消息回调异常: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            try:
                channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
            except Exception as nack_error:
                logger.error(f"❌ 消息拒绝失败: {str(nack_error)}")

    def _handle_message_failure(self, channel, method, db_message: DatabaseMessage, failure_reason: str):
        """处理消息失败的情况，实现重试机制和死信队列"""
        try:
            # 递增重试次数
            db_message.retry_count += 1

            logger.warning(
                f"🔄 消息处理失败: {db_message.message_id}, 原因: {failure_reason}, 重试次数: {db_message.retry_count}/{db_message.max_retry}"
            )

            # 检查是否超过最大重试次数
            if db_message.retry_count >= db_message.max_retry:
                # 发送到死信队列
                logger.error(f"💀 消息超过最大重试次数，发送到死信队列: {db_message.message_id}")
                self._send_to_dead_letter_queue(db_message, failure_reason)

                # 确认消息（不再重试）
                channel.basic_ack(delivery_tag=method.delivery_tag)

                # 标记为已处理（避免重复处理）
                self._processed_messages.add(db_message.message_id)

                # 更新统计
                with self._stats_lock:
                    self._stats["dead_letter_messages"] += 1
            else:
                # 重新发布消息到队列（带有更新的重试次数）
                logger.info(f"🔁 重新发布消息到队列: {db_message.message_id}, 重试次数: {db_message.retry_count}")
                self._republish_message_with_retry(db_message)

                # 确认原消息
                channel.basic_ack(delivery_tag=method.delivery_tag)

                # 更新统计
                with self._stats_lock:
                    self._stats["retry_messages"] += 1

        except Exception as e:
            logger.error(f"❌ 处理消息失败时发生异常: {db_message.message_id}, 错误: {str(e)}")
            # 如果处理失败逻辑本身出错，则简单重新排队
            try:
                channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
            except Exception as nack_error:
                logger.error(f"❌ 消息拒绝失败: {str(nack_error)}")

    def _send_to_dead_letter_queue(self, db_message: DatabaseMessage, failure_reason: str):
        """发送消息到死信队列"""
        try:
            # 添加失败信息到消息数据
            dead_letter_data = db_message.model_dump()
            dead_letter_data["failure_reason"] = failure_reason
            dead_letter_data["failed_at"] = get_shanghai_now().isoformat()

            # 发布到死信队列
            if self.channel:
                self.channel.basic_publish(
                    exchange=settings.rabbitmq_dead_letter_exchange,
                    routing_key=settings.rabbitmq_dead_letter_routing_key,
                    body=json.dumps(dead_letter_data, ensure_ascii=False),
                    properties=pika.BasicProperties(
                        delivery_mode=2 if settings.rabbitmq_durable else 1,  # 持久化
                        timestamp=int(datetime.now().timestamp()),
                    ),
                )
                logger.info(f"💀 消息已发送到死信队列: {db_message.message_id}")
            else:
                logger.error(f"❌ 无法发送到死信队列，channel为空: {db_message.message_id}")

        except Exception as e:
            logger.error(f"❌ 发送消息到死信队列失败: {db_message.message_id}, 错误: {str(e)}")

    def _republish_message_with_retry(self, db_message: DatabaseMessage):
        """重新发布消息到队列（带有更新的重试次数）"""
        try:
            # 更新消息的时间戳
            db_message.created_at = get_shanghai_now()

            # 发布到主队列
            if self.channel:
                self.channel.basic_publish(
                    exchange=settings.rabbitmq_db_exchange,
                    routing_key=settings.rabbitmq_routing_key,
                    body=json.dumps(db_message.model_dump(), ensure_ascii=False),
                    properties=pika.BasicProperties(
                        delivery_mode=2 if settings.rabbitmq_durable else 1,  # 持久化
                        timestamp=int(datetime.now().timestamp()),
                    ),
                )
                logger.debug(f"🔁 消息已重新发布: {db_message.message_id}")
            else:
                logger.error(f"❌ 无法重新发布消息，channel为空: {db_message.message_id}")

        except Exception as e:
            logger.error(f"❌ 重新发布消息失败: {db_message.message_id}, 错误: {str(e)}")

    def _process_message(self, message: DatabaseMessage) -> bool:
        """处理具体的数据库操作消息（在线程池中执行）"""
        try:
            logger.debug(
                f"处理消息: {message.message_id}, 操作: {message.operation_type.value}, 任务: {message.ticket_id}"
            )

            # 根据操作类型分发处理
            if message.operation_type == DatabaseOperationType.UPDATE_STATUS:
                return self._handle_update_status(message)
            elif message.operation_type == DatabaseOperationType.UPDATE_SELENIUM_SESSION:
                return self._handle_update_selenium_session(message)
            elif message.operation_type == DatabaseOperationType.APPEND_LOG:
                return self._handle_append_log(message)
            elif message.operation_type == DatabaseOperationType.UPDATE_RESULT:
                return self._handle_update_result(message)
            elif message.operation_type == DatabaseOperationType.UPDATE_ERROR:
                return self._handle_update_error(message)
            elif message.operation_type == DatabaseOperationType.UPDATE_FILE_PATHS:
                return self._handle_update_file_paths(message)
            elif message.operation_type == DatabaseOperationType.APPEND_SCREENSHOT_PATH:
                return self._handle_append_screenshot_path(message)
            elif message.operation_type == DatabaseOperationType.APPEND_VIDEO_PATH:
                return self._handle_append_video_path(message)
            elif message.operation_type == DatabaseOperationType.APPEND_FILE_PATH:
                return self._handle_append_file_path(message)
            else:
                logger.error(f"未知的操作类型: {message.operation_type}")
                return False

        except Exception as e:
            logger.error(f"处理消息异常: {message.message_id}, 错误: {str(e)}")
            return False

    def _get_task_current_state(self, ticket_id: str) -> dict | None:
        """获取任务当前状态，用于幂等性检查"""
        try:
            sql = """
                SELECT started_at, completed_at, status, selenium_session_id
                FROM task
                WHERE ticket_id = %s;
            """

            with self._db_operator.connection.cursor() as cursor:
                cursor.execute(sql, (ticket_id,))
                result = cursor.fetchone()
                return dict(result) if result else None

        except Exception as e:
            logger.warning(f"获取任务状态失败: {ticket_id}, 错误: {str(e)}")
            return None

    def _handle_update_status(self, message: DatabaseMessage) -> bool:
        """处理更新状态消息 - 增强版本，支持幂等性检查"""
        try:
            data = UpdateStatusMessage(**message.data)
            now = get_shanghai_now()
            kwargs = {}

            # 获取当前任务状态，用于幂等性检查
            current_task = self._get_task_current_state(message.ticket_id)

            if data.status == ExecutionStatus.SUCCESS:
                kwargs.update({"result": data.result_or_error, "error_message": None, "completed_at": now})
            elif data.status == ExecutionStatus.RUNNING:
                # 幂等性检查：如果started_at已经设置，不要覆盖它
                if current_task and current_task.get("started_at"):
                    logger.info(f"任务 {message.ticket_id} 的started_at已存在，跳过重复设置")
                    kwargs.update({"replica_id": settings.replica_id})
                else:
                    kwargs.update({"replica_id": settings.replica_id, "started_at": now})
                    logger.info(f"任务 {message.ticket_id} 设置started_at为 {now}")
            else:  # FAILED or CANCELLED
                # 幂等性检查：如果completed_at已经设置，不要覆盖它
                if current_task and current_task.get("completed_at"):
                    logger.info(f"任务 {message.ticket_id} 的completed_at已存在，跳过重复设置")
                    kwargs.update(
                        {
                            "result": None,
                            "error_message": str(data.result_or_error) if data.result_or_error else None,
                        }
                    )
                else:
                    kwargs.update(
                        {
                            "result": None,
                            "error_message": str(data.result_or_error) if data.result_or_error else None,
                            "completed_at": now,
                        }
                    )

            # 如果有步骤日志消息，也一起更新
            if data.step_log_message:
                # 先更新日志
                self._db_operator.append_task_log(message.ticket_id, data.step_log_message, append=True)

            # 更新数据库状态
            db_update_success = self._db_operator.update_task_status(message.ticket_id, data.status, **kwargs)

            # 对于最终状态（SUCCESS, FAILURE），发送回调到主服务
            if data.status in [ExecutionStatus.SUCCESS, ExecutionStatus.FAILED] and db_update_success:
                try:
                    callback_service = get_callback_service()

                    # 准备回调参数
                    callback_kwargs = {"type": "task", "ticket_id": message.ticket_id, "status": data.status.value}

                    if data.status == ExecutionStatus.FAILED:
                        callback_kwargs["error_message"] = (
                            str(data.result_or_error) if data.result_or_error else "执行失败"
                        )

                    # 发送回调（使用同步方式，因为我们在线程池中）
                    callback_success = callback_service.send_task_callback_sync(**callback_kwargs)

                    if callback_success:
                        logger.info(f"任务状态回调发送成功: {message.ticket_id} -> {data.status}")
                    else:
                        # 回调失败不影响主流程，记录警告而不是错误
                        logger.warning(f"任务状态回调发送失败: {message.ticket_id} -> {data.status} (不影响任务执行)")

                except Exception as callback_error:
                    logger.warning(
                        f"发送任务回调时发生异常: {message.ticket_id}, 错误: {str(callback_error)} (不影响任务执行)"
                    )
                    # 不影响主流程，回调失败不应该导致消息处理失败

            return db_update_success

        except Exception as e:
            logger.error(f"处理更新状态消息失败: {message.ticket_id}, 错误: {str(e)}")
            # 创建消息处理失败告警
            from app.utils.alert_system import create_message_processing_alert

            create_message_processing_alert(message.ticket_id, "UPDATE_STATUS", str(e))
            return False

    def _handle_update_selenium_session(self, message: DatabaseMessage) -> bool:
        """处理更新Selenium会话消息 - 增强版本，支持幂等性检查"""
        try:
            data = UpdateSeleniumSessionMessage(**message.data)

            # 幂等性检查：如果selenium_session_id已经设置，不要覆盖它
            current_task = self._get_task_current_state(message.ticket_id)
            if current_task and current_task.get("selenium_session_id"):
                logger.info(f"任务 {message.ticket_id} 的selenium_session_id已存在，跳过重复设置")
                return True

            success = self._db_operator.update_selenium_session(message.ticket_id, data.session_id)
            if success:
                logger.info(f"任务 {message.ticket_id} 成功设置selenium_session_id为 {data.session_id}")
            return success
        except Exception as e:
            logger.error(f"处理更新Selenium会话消息失败: {message.ticket_id}, 错误: {str(e)}")
            # 创建消息处理失败告警
            from app.utils.alert_system import create_message_processing_alert

            create_message_processing_alert(message.ticket_id, "UPDATE_SELENIUM_SESSION", str(e))
            return False

    def _handle_append_log(self, message: DatabaseMessage) -> bool:
        """处理追加日志消息"""
        try:
            data = AppendLogMessage(**message.data)
            return self._db_operator.append_task_log(message.ticket_id, data.message, data.append)
        except Exception as e:
            logger.error(f"处理追加日志消息失败: {message.ticket_id}, 错误: {str(e)}")
            return False

    def _handle_update_result(self, message: DatabaseMessage) -> bool:
        """处理更新结果消息"""
        try:
            data = UpdateResultMessage(**message.data)
            return self._db_operator.update_task_result(message.ticket_id, data.result)
        except Exception as e:
            logger.error(f"处理更新结果消息失败: {message.ticket_id}, 错误: {str(e)}")
            return False

    def _handle_update_error(self, message: DatabaseMessage) -> bool:
        """处理更新错误消息"""
        try:
            data = UpdateErrorMessage(**message.data)
            return self._db_operator.update_task_error(message.ticket_id, data.error_message)
        except Exception as e:
            logger.error(f"处理更新错误消息失败: {message.ticket_id}, 错误: {str(e)}")
            return False

    def _handle_update_file_paths(self, message: DatabaseMessage) -> bool:
        """处理更新文件路径消息"""
        try:
            from app.schemas.mq_message import UpdateFilePathsMessage

            data = UpdateFilePathsMessage(**message.data)
            return self._db_operator.update_task_file_paths(
                message.ticket_id,
                data.video_file_paths,
                data.screenshot_file_paths,
                data.download_file_paths,
            )
        except Exception as e:
            logger.error(f"处理更新文件路径消息失败: {message.ticket_id}, 错误: {str(e)}")
            return False

    def _handle_append_screenshot_path(self, message: DatabaseMessage) -> bool:
        """处理追加截图路径消息 - 增强版本，详细日志记录"""
        try:
            from app.schemas.mq_message import AppendScreenshotPathMessage

            data = AppendScreenshotPathMessage(**message.data)
            logger.debug(f"处理追加截图路径消息: {message.ticket_id} -> {data.screenshot_path}")

            success = self._db_operator.append_screenshot_path(message.ticket_id, data.screenshot_path)
            if success:
                logger.info(f"成功追加截图路径: {message.ticket_id} -> {data.screenshot_path}")
            else:
                logger.warning(f"追加截图路径失败: {message.ticket_id} -> {data.screenshot_path}")
            return success
        except Exception as e:
            logger.error(f"处理追加截图路径消息异常: {message.ticket_id}, 错误: {str(e)}")
            return False

    def _handle_append_video_path(self, message: DatabaseMessage) -> bool:
        """处理追加视频路径消息"""
        try:
            from app.schemas.mq_message import AppendVideoPathMessage

            data = AppendVideoPathMessage(**message.data)
            return self._db_operator.append_video_path(message.ticket_id, data.video_path)
        except Exception as e:
            logger.error(f"处理追加视频路径消息失败: {message.ticket_id}, 错误: {str(e)}")
            return False

    def _handle_append_file_path(self, message: DatabaseMessage) -> bool:
        """处理追加下载文件路径消息"""
        try:
            from app.schemas.mq_message import AppendFilePathMessage

            data = AppendFilePathMessage(**message.data)
            return self._db_operator.append_file_path(message.ticket_id, data.file_path)
        except Exception as e:
            logger.error(f"处理追加下载文件路径消息失败: {message.ticket_id}, 错误: {str(e)}")
            return False

    def _start_cleanup_task(self):
        """启动清理任务"""

        def cleanup_old_messages():
            stats_log_counter = 0
            while self._running:
                try:
                    time.sleep(60)  # 每分钟清理一次
                    now = datetime.now()

                    # 清理超过1小时的消息记录
                    expired_messages = [
                        msg_id
                        for msg_id, timestamp in self._message_timestamps.items()
                        if now - timestamp > timedelta(hours=1)
                    ]

                    for msg_id in expired_messages:
                        self._processed_messages.discard(msg_id)
                        del self._message_timestamps[msg_id]

                    if expired_messages:
                        logger.debug(f"清理了 {len(expired_messages)} 条过期消息记录")

                    # 每5分钟记录一次统计信息
                    stats_log_counter += 1
                    if stats_log_counter >= 5:
                        self.log_stats()
                        stats_log_counter = 0

                except Exception as e:
                    logger.debug(f"清理消息记录时发生错误: {str(e)}")

        cleanup_thread = threading.Thread(target=cleanup_old_messages, daemon=True, name="message_cleanup")
        cleanup_thread.start()

    def get_stats(self) -> dict:
        """获取消费者统计信息"""
        with self._stats_lock:
            stats = self._stats.copy()

            # 计算平均处理时间
            if stats["processing_times"]:
                stats["avg_processing_time"] = sum(stats["processing_times"]) / len(stats["processing_times"])
                stats["max_processing_time"] = max(stats["processing_times"])
                stats["min_processing_time"] = min(stats["processing_times"])
            else:
                stats["avg_processing_time"] = 0
                stats["max_processing_time"] = 0
                stats["min_processing_time"] = 0

            # 计算成功率
            if stats["total_messages"] > 0:
                stats["success_rate"] = stats["successful_messages"] / stats["total_messages"] * 100
            else:
                stats["success_rate"] = 0

            # 添加线程池状态
            if self._executor:
                stats["thread_pool_active"] = True
                # 注意：ThreadPoolExecutor没有直接的方法获取活跃线程数，这里只是示例
                stats["thread_pool_max_workers"] = self._executor._max_workers
            else:
                stats["thread_pool_active"] = False
                stats["thread_pool_max_workers"] = 0

            # 移除原始处理时间列表（太长了）
            del stats["processing_times"]

            return stats

    def log_stats(self):
        """记录统计信息到日志"""
        stats = self.get_stats()
        logger.info(
            f"📊 消费者统计信息: "
            f"总消息={stats['total_messages']}, "
            f"成功={stats['successful_messages']}, "
            f"失败={stats['failed_messages']}, "
            f"超时={stats['timeout_messages']}, "
            f"死信={stats['dead_letter_messages']}, "
            f"重试={stats['retry_messages']}, "
            f"成功率={stats['success_rate']:.1f}%, "
            f"平均处理时间={stats['avg_processing_time']:.2f}秒"
        )

    def stop_consumer(self):
        """停止消费者"""
        try:
            logger.info("正在停止RabbitMQ消费者...")
            self._running = False

            # 等待消费者线程结束
            if self._consumer_thread and self._consumer_thread.is_alive():
                logger.debug("等待消费者线程结束...")
                self._consumer_thread.join(timeout=10)

                if self._consumer_thread.is_alive():
                    logger.warning("消费者线程未能在10秒内结束")

            self._cleanup()
            logger.info("RabbitMQ消费者已停止")

        except Exception as e:
            logger.error(f"停止RabbitMQ消费者失败: {str(e)}")

    def _cleanup(self):
        """清理资源"""
        try:
            # 关闭线程池
            if self._executor:
                self._executor.shutdown(wait=True)
                self._executor = None

            # 关闭数据库连接
            if self._db_operator:
                self._db_operator.close()
                self._db_operator = None

            # 关闭RabbitMQ连接
            if self.channel and not self.channel.is_closed:
                self.channel.close()
                self.channel = None

            if self.connection and not self.connection.is_closed:
                self.connection.close()
                self.connection = None

            logger.debug("资源清理完成")

        except Exception as e:
            logger.debug(f"清理资源时发生错误: {str(e)}")


# 全局消费者实例
_global_consumer: DatabaseMessageConsumer | None = None


def get_consumer() -> DatabaseMessageConsumer:
    """获取全局消费者实例"""
    global _global_consumer
    if _global_consumer is None:
        _global_consumer = DatabaseMessageConsumer()
    return _global_consumer


def start_global_consumer():
    """启动全局消费者"""
    consumer = get_consumer()
    consumer.start_consumer()


def stop_global_consumer():
    """停止全局消费者"""
    global _global_consumer
    if _global_consumer:
        _global_consumer.stop_consumer()
        _global_consumer = None
