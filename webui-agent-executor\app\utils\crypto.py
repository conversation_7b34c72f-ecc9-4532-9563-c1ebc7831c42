"""
RSA非对称加密工具类

用于密码等敏感信息的加密和解密
"""

import base64
from pathlib import Path

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa

from app.config.settings import settings
from app.utils.log import logger


class RSACrypto:
    """RSA非对称加密类"""

    def __init__(self, key_size: int = 2048):
        """
        初始化RSA加密类

        Args:
            key_size: RSA密钥长度，默认2048位
        """
        self.key_size = key_size
        self.private_key = None
        self.public_key = None
        self.keys_dir = Path(settings.keys_dir)
        self.keys_dir.mkdir(exist_ok=True)

        # 自动加载或生成密钥
        self._load_or_generate_keys()

    def _load_or_generate_keys(self):
        """加载现有密钥或生成新密钥"""
        private_key_path = self.keys_dir / "private_key.pem"
        public_key_path = self.keys_dir / "public_key.pem"

        if private_key_path.exists() and public_key_path.exists():
            try:
                self._load_keys_from_files(private_key_path, public_key_path)
                logger.info("成功加载现有RSA密钥")
            except Exception as e:
                logger.warning(f"加载密钥失败: {e}，将生成新密钥")
                self._generate_new_keys()
        else:
            logger.info("未找到现有密钥，生成新的RSA密钥对")
            self._generate_new_keys()

    def _load_keys_from_files(self, private_key_path: Path, public_key_path: Path):
        """从文件加载密钥"""
        # 加载私钥
        with open(private_key_path, "rb") as f:
            self.private_key = serialization.load_pem_private_key(f.read(), password=None, backend=default_backend())

        # 加载公钥
        with open(public_key_path, "rb") as f:
            self.public_key = serialization.load_pem_public_key(f.read(), backend=default_backend())

    def _generate_new_keys(self):
        """生成新的RSA密钥对并保存"""
        # 生成私钥
        self.private_key = rsa.generate_private_key(
            public_exponent=65537, key_size=self.key_size, backend=default_backend()
        )

        # 从私钥获取公钥
        self.public_key = self.private_key.public_key()

        # 保存密钥到文件
        self._save_keys_to_files()

    def _save_keys_to_files(self):
        """保存密钥到文件"""
        private_key_path = self.keys_dir / "private_key.pem"
        public_key_path = self.keys_dir / "public_key.pem"

        # 保存私钥
        private_pem = self.private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption(),
        )
        with open(private_key_path, "wb") as f:
            f.write(private_pem)

        # 保存公钥
        public_pem = self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )
        with open(public_key_path, "wb") as f:
            f.write(public_pem)

        logger.info(f"RSA密钥对已保存到: {self.keys_dir}")

    def encrypt(self, plaintext: str) -> str:
        """
        使用公钥加密文本

        Args:
            plaintext: 要加密的明文

        Returns:
            str: Base64编码的密文
        """
        if not self.public_key:
            raise ValueError("公钥未初始化")

        try:
            # 将字符串转换为字节
            plaintext_bytes = plaintext.encode("utf-8")

            # 使用公钥加密
            ciphertext = self.public_key.encrypt(  # type: ignore
                plaintext_bytes,
                padding.PKCS1v15(),
            )

            # 使用Base64编码
            encrypted_text = base64.b64encode(ciphertext).decode("utf-8")
            logger.debug("文本加密成功")
            return encrypted_text

        except Exception as e:
            logger.error(f"加密失败: {e}")
            raise

    def decrypt(self, encrypted_text: str) -> str:
        """
        使用私钥解密文本

        Args:
            encrypted_text: Base64编码的密文

        Returns:
            str: 解密后的明文
        """
        if not self.private_key:
            raise ValueError("私钥未初始化")

        try:
            # Base64解码
            ciphertext = base64.b64decode(encrypted_text.encode("utf-8"))

            # 使用私钥解密
            plaintext_bytes = self.private_key.decrypt(  # type: ignore
                ciphertext,
                padding.PKCS1v15(),
            )

            # 将字节转换为字符串
            plaintext = plaintext_bytes.decode("utf-8")
            logger.debug("文本解密成功")
            return plaintext

        except Exception as e:
            logger.error(f"解密失败: {e}")
            raise

    def get_public_key_pem(self) -> str:
        """
        获取PEM格式的公钥字符串

        Returns:
            str: PEM格式的公钥
        """
        if not self.public_key:
            raise ValueError("公钥未初始化")

        public_pem = self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )
        return public_pem.decode("utf-8")

    def encrypt_from_public_key_pem(self, plaintext: str, public_key_pem: str) -> str:
        """
        使用PEM格式的公钥加密文本

        Args:
            plaintext: 要加密的明文
            public_key_pem: PEM格式的公钥字符串

        Returns:
            str: Base64编码的密文
        """
        try:
            # 加载公钥
            public_key = serialization.load_pem_public_key(public_key_pem.encode("utf-8"), backend=default_backend())

            # 加密
            plaintext_bytes = plaintext.encode("utf-8")
            ciphertext = public_key.encrypt(  # type: ignore
                plaintext_bytes,
                padding.PKCS1v15(),
            )

            # Base64编码
            encrypted_text = base64.b64encode(ciphertext).decode("utf-8")
            logger.debug("使用提供的公钥加密成功")
            return encrypted_text

        except Exception as e:
            logger.error(f"使用提供的公钥加密失败: {e}")
            raise


# 全局实例
_rsa_crypto = None


def get_rsa_crypto() -> RSACrypto:
    """
    获取RSA加密实例（单例模式）

    Returns:
        RSACrypto: RSA加密实例
    """
    global _rsa_crypto
    if _rsa_crypto is None:
        _rsa_crypto = RSACrypto()
    return _rsa_crypto


def encrypt(password: str) -> str:
    """
    加密密码的便捷函数

    Args:
        password: 明文密码

    Returns:
        str: 加密后的密码
    """
    crypto = get_rsa_crypto()
    return crypto.encrypt(password)


def decrypt(encrypted_password: str) -> str:
    """
    解密密码的便捷函数

    Args:
        encrypted_password: 加密的密码

    Returns:
        str: 解密后的明文密码
    """
    crypto = get_rsa_crypto()
    return crypto.decrypt(encrypted_password)
