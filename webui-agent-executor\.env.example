# 数据库连接字符串
DATABASE_URL="postgres://postgres:xxxx@localhost:5432/postgres"

# LLM API 配置
OPENAI_API_KEY=xxxx
OPENAI_BASE_URL=https://zhenze-huhehaote.cmecloud.cn/v1
OPENAI_MODEL=deepseek-v3-0324
OPENAI_TEMPERATURE=0.1

# Planner LLM 配置
PLANNER_LLM_API_KEY=xxxx
PLANNER_LLM_BASE_URL=http://************:20008/v1
PLANNER_LLM_MODEL=Qwen3-32B
PLANNER_LLM_TEMPERATURE=0.1

# 匿名遥测
ANONYMIZED_TELEMETRY=false

# RabbitMQ密码
RABBITMQ_PASSWORD=xxxx

# Minio密码 
MINIO_SECRET_KEY=xxxx
MINIO_ENABLED=true

# Token管理配置
ENABLE_TOKEN_TRUNCATION=true
MAX_INPUT_TOKENS=24000
TOKEN_BUFFER=576
