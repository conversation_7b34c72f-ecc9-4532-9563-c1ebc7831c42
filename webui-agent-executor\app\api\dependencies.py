"""
API 依赖注入

定义 FastAPI 路由中使用的依赖项
"""

from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise

from app.config.settings import settings


def get_database_config():
    """
    获取数据库配置字典

    Returns:
        dict: Tortoise ORM 配置字典
    """
    return {
        "connections": {"default": settings.database_url},
        "apps": {
            "models": {
                "models": ["app.models.task", "app.models.script"],
                "default_connection": "default",
            }
        },
        "use_tz": False,
        "timezone": settings.database_timezone,
    }


async def init_database_for_subprocess():
    """
    为子进程初始化数据库连接

    使用 Tortoise.init() 而不是 register_tortoise()
    因为子进程中没有 FastAPI 应用实例
    """
    await Tortoise.init(config=get_database_config())


def init_database(app):
    """
    为 FastAPI 应用初始化数据库连接

    Args:
        app: FastAPI 应用实例
    """
    register_tortoise(
        app,
        config=get_database_config(),
        generate_schemas=True,  # 自动创建表结构
        add_exception_handlers=True,  # 添加异常处理
    )
