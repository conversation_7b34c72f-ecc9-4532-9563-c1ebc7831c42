"""
历史记录助手函数

用于处理浏览器自动化结果的格式转换
"""

from typing import Any


def convert_single_item(from_item: dict[str, Any]) -> dict[str, Any] | None:
    """
    单个对象的转换逻辑，返回to.json格式的dict。
    如果action为null或空，返回None。
    """
    # 1. action
    action = None
    model_output = from_item.get("model_output")
    if isinstance(model_output, dict) and "action" in model_output:
        action = model_output["action"]
    if not action:
        return None  # 跳过无action的项

    to_data = {}
    to_data["action"] = action

    # 2. result
    if "result" in from_item:
        to_data["result"] = [{"extracted_content": r.get("extracted_content", None)} for r in from_item["result"]]
    else:
        to_data["result"] = None

    # 3. 直接提升tabs、interacted_element、url、title到顶层
    state = from_item.get("state", {})
    # tabs
    tabs = state.get("tabs", None)
    if tabs is not None:
        to_data["tabs"] = []
        for tab in tabs:
            to_data["tabs"].append(
                {
                    "page_id": tab.get("page_id", None),
                    "url": tab.get("url", None),
                    "title": tab.get("title", None),
                }
            )
    else:
        to_data["tabs"] = None
    # interacted_element
    interacted = state.get("interacted_element", None)
    if interacted is not None:
        to_data["interacted_element"] = []
        for elem in interacted:
            if elem is None:
                to_data["interacted_element"].append({"xpath": None, "highlight_index": None, "attributes": None})
            else:
                to_data["interacted_element"].append(
                    {
                        "xpath": elem.get("xpath", None),
                        "highlight_index": elem.get("highlight_index", None),
                        "attributes": elem.get("attributes", None),
                    }
                )
    else:
        to_data["interacted_element"] = None
    # url
    to_data["url"] = state.get("url", None)
    # title
    to_data["title"] = state.get("title", None)

    return to_data


def convert_from_to(from_data: list[dict[str, Any]]) -> list[dict[str, Any]]:
    """
    精简历史记录json，action为null或空的对象会被跳过。
    """
    results = []
    for item in from_data:
        result = convert_single_item(item)
        if result is not None:
            results.append(result)

    return results
