[project]
name = "WebUIAgentExecutor"
version = "1.0.0"
description = "WebUI智能体执行服务"
readme = "README.md"
requires-python = ">=3.12"
authors = [
    {name = "WebUI Agent Integration Team"}
]
keywords = [
    "browser-use",
    "deepseek",
    "browser-automation",
    "fastapi",
    "playwright",
    "distributed-system",
    "task-queue",
    "llm-integration",
    "web-automation",
    "rabbitmq",
    "tortoise-orm"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: Browsers",
    "Topic :: Software Development :: Testing",
    "Topic :: System :: Distributed Computing",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
dependencies = [
    "browser-use==0.5.5",
    "fastapi>=0.115.12",
    "pytz>=2025.2",
    "tortoise-orm[asyncpg]>=0.25.1",
    "uvicorn>=0.34.3",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.3",
    "paddleocr>=3.0.2",
    "paddlepaddle==3.0.0",
    "cryptography>=41.0.0",
    "requests>=2.31.0",
    "pika>=1.3.0",
    "psycopg2-binary>=2.9.0",
    "tiktoken>=0.5.0",
    "setuptools>=80.9.0",
    "minio>=7.2.16",
]

[project.optional-dependencies]
dev = [
    # 代码质量工具
    "ruff>=0.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",

    # 类型检查
    "pyright>=1.1.0",

    # 拼写检查
    "codespell>=2.2.0",

    # 测试框架
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "httpx>=0.24.0",  # 用于测试HTTP客户端

    # 开发工具
    "pre-commit>=3.0.0",
]

# 构建配置
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

# uv包管理器配置
[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true

# 代码格式化配置
[tool.black]
line-length = 120
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# 导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 120
known_first_party = ["app"]
known_third_party = ["browser_use", "fastapi", "tortoise", "loguru"]

# Ruff配置 - 代码检查和格式化
[tool.ruff]
target-version = "py312"
line-length = 120

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"app/main.py" = ["E402"]  # 需要先加载环境变量再导入其他模块

# Pyright类型检查配置
[tool.pyright]
include = ["app"]
exclude = ["**/__pycache__"]
venvPath = "."
venv = ".venv"
pythonVersion = "3.12"
typeCheckingMode = "basic"
reportMissingImports = true
reportMissingTypeStubs = false
reportUnusedImport = false  # 暂时禁用，因为schemas中的导入是为了重新导出
reportUnusedClass = true
reportUnusedFunction = true
reportDuplicateImport = true
reportOptionalMemberAccess = false  # 暂时禁用，需要大量代码重构
reportArgumentType = false  # 暂时禁用，需要详细的类型注解
reportCallIssue = false  # 暂时禁用，需要API文档确认参数

# 拼写检查配置
[tool.codespell]
skip = '*.git,*.svg,*.pdf,*.pyc,*.egg-info,*.lock'
ignore-words-list = "nd,ue,te,ba,fo,fro,ro"

# 测试配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

# 测试覆盖率配置
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
