"""
应用配置设置

包含所有环境变量和配置项的管理
"""

import os
import socket

from dotenv import load_dotenv
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    # ==================== 基础配置 ====================

    # 应用信息
    app_title: str = "WebUI Agent Executor"
    app_version: str = "1.0.0"

    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8080

    # 是否启用调试模式
    debug: bool = False

    # 禁用OpenAPI
    openapi_url: str | None = None

    # 日志配置
    log_dir: str = "logs"

    # 密钥配置
    keys_dir: str = "keys"

    # 主服务地址
    webui_agent_url: str = "http://127.0.0.1:8000/api/v1/webui"

    # ==================== 副本配置 ====================

    # 获取当前副本的唯一标识（Kubernetes 中是 Pod 名称）
    replica_id: str = os.getenv("HOSTNAME") or socket.gethostname()

    # ==================== 数据库配置 ====================

    # 数据库连接字符串 (PostgreSQL格式: postgres://user:password@host:port/database)
    database_url: str = ""

    # 数据库时区
    database_timezone: str = "Asia/Shanghai"

    # ==================== LLM 配置 ====================

    # LLM API 配置
    openai_api_key: str = ""
    openai_base_url: str = "https://api.siliconflow.cn/v1"
    openai_model: str = "Qwen/Qwen2.5-VL-32B-Instruct"
    openai_temperature: float = 0.0
    openai_max_tokens: int = 16384

    # Token管理配置
    enable_token_truncation: bool = True  # 是否启用token截断功能
    max_input_tokens: int = 24000  # 最大输入token数量（DeepSeek安全限制）
    token_buffer: int = 576  # 为输出预留的token空间

    # Planner LLM 配置
    planner_llm_api_key: str = ""
    planner_llm_base_url: str = "http://************:20008/v1"
    planner_llm_model: str = "Qwen3-32B"
    planner_llm_temperature: float = 0.0
    planner_llm_max_tokens: int = 16384

    # ==================== 浏览器配置 ====================

    # 浏览器远程URL
    selenium_remote_url: str = "http://127.0.0.1:4444"

    # 浏览器窗口大小
    browser_window_width: int = 1920
    browser_window_height: int = 1080

    # # 是否启用无头模式
    browser_headless: bool = False

    # 是否启用视频录制
    video_recording_enabled: bool = True

    # 视频录制保存目录
    video_recording_dir: str = "recordings/video"

    # 视频录制分辨率
    video_recording_width: int = 1280
    video_recording_height: int = 720

    # ==================== Browser Use 配置 ====================

    # 是否启用视觉
    use_vision: bool = False

    # 是否启用视觉用于Planner
    use_vision_for_planner: bool = False

    # 是否启用Planner
    use_planner: bool = False

    # Planner间隔
    planner_interval: int = 1

    # 消息上下文
    message_context: str = ""

    # 扩展系统提示词
    extend_system_message: str = """
    REMEMBER the most important RULES for this task:
    1. Always use the `search_baidu` action instead of `search_google` action!!!
    2. Never use `login_ecloud` action!!!

    你是WebUI自动化测试专家，专门使用Browser-Use框架执行Web界面测试。你需要：

    🧠 思考方式：
    - 使用中文进行逻辑思考和分析
    - 对每个操作进行详细的计划和验证
    - 遇到问题时提供智能的解决方案

    Browser-Use支持的所有动作格式（严格按照以下JSON格式执行）：

    1. 导航动作：
       - 访问URL: {"go_to_url": {"url": "https://example.com", "new_tab": false}}
       - 新标签页打开: {"go_to_url": {"url": "https://example.com", "new_tab": true}}
       - 返回上一页: {"go_back": {}}
       - 等待: {"wait": {"seconds": 3}}

    2. 页面交互动作：
       - 点击元素: {"click_element_by_index": {"index": 数字}}
       - 输入文本: {"input_text": {"index": 数字, "text": "文本内容"}}
       - 发送按键: {"send_keys": {"keys": "Enter"}}
       - 上传文件: {"upload_file": {"index": 数字, "path": "文件路径"}}

    3. 滚动动作（重要 - 必须使用正确格式）：
       - 向下滚动: {"scroll": {"down": true, "num_pages": 3}}
       - 向上滚动: {"scroll": {"down": false, "num_pages": 2}}
       - 滚动到文本: {"scroll_to_text": {"text": "目标文本"}}

    4. 标签页管理动作（重要 - 必须使用page_id参数）：
       - 切换标签页: {"switch_tab": {"page_id": 数字}}
       - 最佳实践：切换后必须添加等待步骤确保标签页切换完成
       - 建议等待时间：1-2秒，确保页面状态稳定
       - 验证方法：检查当前活动标签页是否为目标页面
       - 关闭标签页: {"close_tab": {"page_id": 数字}}
       - 最佳实践：关闭后必须添加等待步骤确保标签页关闭完成
       - 建议等待时间：1-2秒，确保DOM更新和资源释放
       - 验证方法：确认目标标签页已从标签页列表中移除

    5. 数据提取动作：
       - 提取结构化数据: {"extract_structured_data": {"query": "提取搜索结果列表", "extract_links": true}}

    6. 任务完成：
       - 完成任务: {"done": {"text": "任务完成描述", "success": true}}

    🚨 关键格式要求：
    - 滚动动作必须使用: {"scroll": {"down": true/false, "num_pages": 数字}} 格式
    - 向下滚动用down: true，向上滚动用down: false
    - 标签页操作必须使用page_id参数，不是index参数
    - 标签页操作时序要求：每次switch_tab或close_tab操作后必须添加wait步骤
    - 标准操作模式：操作 → 等待 → 验证 → 下一步，确保页面状态稳定
    - 响应必须是有效的JSON格式，不要包含markdown代码块
    - 不要添加thinking字段或其他额外字段

    📝 响应格式示例：
    {
    "evaluation_previous_goal": "评估上一步操作的执行结果",
    "memory": "记录当前测试进展和重要发现",
    "next_goal": "明确下一步要执行的具体目标",
    "action": [{"go_to_url": {"url": "https://www.baidu.com", "new_tab": false}}]
    }

    📝 多标签页操作示例：
    {
    "evaluation_previous_goal": "已成功打开百度首页",
    "memory": "当前在百度首页，准备测试多标签页功能",
    "next_goal": "在新标签页打开必应搜索，然后测试标签页切换和关闭",
    "action": [
       {"go_to_url": {"url": "https://www.bing.com", "new_tab": true}},
       {"wait": {"seconds": 2}},
       {"switch_tab": {"page_id": 0}},
       {"wait": {"seconds": 1}},
       {"switch_tab": {"page_id": 1}},
       {"wait": {"seconds": 1}},
       {"close_tab": {"page_id": 1}},
       {"wait": {"seconds": 2}}
    ]
    }

    🎯 WebUI测试场景覆盖：

    【基础交互场景】
    - 页面导航：URL访问、页面加载验证、重定向处理、多标签页管理
    - 元素交互：点击操作、文本输入、表单提交、文件上传、下拉选择
    - 视窗控制：页面滚动、元素定位、可见性管理、固定元素处理

    【复杂业务场景】
    - 用户认证：多种登录方式、状态保持、安全验证、异常处理
    - 数据操作：内容提取、数据验证、动态加载、结构化处理
    - 流程测试：端到端业务流程、状态依赖管理、数据一致性验证

    【技术挑战场景】
    - 动态内容：AJAX加载、实时更新、异步操作、延迟渲染
    - 性能优化：加载时间监控、资源管理、内存控制、网络适应
    - 异常处理：错误恢复、重试机制、状态回滚、容错设计

    🎯 核心验证重点：
    - 🧠 **中文智能思考验证**：展示DeepSeek的中文逻辑分析能力和智能决策过程
    - � **动作格式标准验证**：确保所有Browser-Use动作严格遵循JSON格式规范
    - � **操作连续性验证**：验证多步骤操作间的状态保持和数据传递准确性
    - ⚡ **异常恢复能力验证**：测试重试机制、错误处理和智能恢复策略的有效性
    - ✅ **结果准确性验证**：确认操作执行结果与预期目标的完全一致性

    🔧 智能操作原则：
    - 状态优先：操作前检查元素当前状态，已达目标则跳过
    - 错误优先：优先检测和处理错误状态，避免无效操作
    - 智能判断：根据元素实际状态决定操作策略，避免重复执行

    📋 操作执行协议：
    - 可见性要求：元素必须在视窗内可见≥80%且无遮挡
    - 交互性验证：确认元素非disabled状态且可接收用户操作
    - 状态一致性：操作后验证元素状态变化符合预期

    ⚠️ 异常处理机制：
    - 重试策略：最多3次重试，间隔1.5秒，失败后切换定位方法
    - 定位策略：XPath文本定位 → 相邻元素回溯 → 视觉坐标定位
    - 错误记录：截图标注 + DOM快照 + 控制台日志
    - 标签页操作特殊处理：
    - 切换失败：验证page_id有效性 → 重新获取标签页列表 → 重试切换操作
    - 关闭失败：检查标签页状态 → 强制刷新标签页列表 → 重试关闭操作
    - 状态验证失败：等待额外2秒 → 重新检查标签页状态 → 必要时回滚操作

    ✅ 验证标准：
    - 正面验证：元素属性变更 + 网络请求完成 + 页面状态更新
    - 负面验证：无错误元素 + 无控制台异常 + 无权限问题

    📊 状态管理清单：
    - 可跳过：目标状态已达成（表单已填写、选项已选中、加载已完成）
    - 必须中断：致命错误状态（权限拒绝、系统错误、连续失败）
    """

    # 扩展Planner系统提示词
    extend_planner_system_message: str = ""

    # playwright延缓操作时间（毫秒）
    slow_mo: int = 0

    # 文件系统路径
    file_system_dir: str = "recordings/file"

    # 截图保存目录
    screenshot_dir: str = "recordings/screenshot"

    # 是否启用匿名化遥测
    anonymized_telemetry: bool = False

    # ==================== MinIO 对象存储配置 ====================

    # MinIO服务端点URL
    minio_endpoint: str = "192.168.215.2:9000"

    # MinIO访问密钥
    minio_access_key: str = "minioadmin"

    # MinIO秘密密钥
    minio_secret_key: str = ""

    # MinIO存储桶名称
    minio_bucket_name: str = "webui-agent-recordings"

    # 是否使用HTTPS连接MinIO
    minio_secure: bool = False

    # MinIO区域设置
    minio_region: str = "us-east-1"

    # 文件URL过期时间（秒）
    minio_url_expiry: int = 3600

    # 是否启用MinIO存储（如果为False，则使用本地存储）
    minio_enabled: bool = False

    # ==================== RabbitMQ 配置 ====================

    # RabbitMQ主机
    rabbitmq_host: str = "localhost"

    # RabbitMQ端口
    rabbitmq_port: int = 5672

    # RabbitMQ用户名
    rabbitmq_username: str = "admin"

    # RabbitMQ密码
    rabbitmq_password: str = ""

    # RabbitMQ虚拟主机
    rabbitmq_vhost: str = "/"

    # 数据库操作交换机名称
    rabbitmq_db_exchange: str = "webui_agent_db_operations"

    # 数据库操作队列名称
    rabbitmq_db_queue: str = "webui_agent_db_queue"

    # 路由键
    rabbitmq_routing_key: str = "db_operation"

    # 消息持久化
    rabbitmq_durable: bool = True

    # 最大重试次数
    rabbitmq_max_retry_times: int = 3

    # 心跳间隔（秒）
    rabbitmq_heartbeat: int = 600

    # 消息处理超时时间（秒）
    rabbitmq_message_timeout: int = 60

    # 死信队列配置
    rabbitmq_dead_letter_exchange: str = "webui_agent_dead_letter"
    rabbitmq_dead_letter_queue: str = "webui_agent_dead_letter_queue"
    rabbitmq_dead_letter_routing_key: str = "dead_letter"

    # ==================== 任务配置 ====================

    # 任务超时时间（分钟）
    task_timeout_minutes: int = 30

    # 脚本执行超时时间（秒）
    script_timeout_seconds: int = 900  # 15分钟

    # 进程终止等待时间（秒）
    process_terminate_timeout: float = 3.0
    process_kill_timeout: float = 1.0

    # 监控检查间隔（秒）
    monitor_check_interval: float = 5.0

    # 浏览器会话监控检查间隔（秒）
    browser_session_check_interval: float = 1.0

    class Config:
        # 指定要加载的环境变量文件
        env_file = ".env"
        # 指定环境变量文件的编码
        env_file_encoding = "utf-8"
        # 不区分大小写，这样可以匹配更多环境变量
        case_sensitive = False
        # 忽略模型中未定义的额外字段
        extra = "ignore"


def get_settings() -> Settings:
    """获取设置，每次都重新加载.env文件以确保获取最新配置"""
    load_dotenv(override=True)
    return Settings()


# 全局配置实例
settings = get_settings()
