"""自定义函数工具类"""

import re

import cv2
import numpy as np
from browser_use import ActionResult, Controller
from browser_use.browser import BrowserSession
from paddleocr import TextRecognition
from pydantic import BaseModel

from app.schemas.cookie import SetCookiesBatchAction
from app.utils.cookie_manager import CookieManager
from app.utils.crypto import decrypt
from app.utils.log import logger

# 全局模型实例
_global_model: TextRecognition | None = None


def create_cookie_manager(browser_session: BrowserSession) -> CookieManager:
    """
    创建新的 CookieManager 实例

    Args:
        browser_session: Browser Use 浏览器会话对象

    Returns:
        CookieManager: 新创建的 Cookie 管理器实例

    Raises:
        ValueError: 当 browser_session 为空时
    """
    if not browser_session:
        raise ValueError("browser_session 不能为空")

    logger.debug("创建新的 CookieManager 实例")
    cookie_manager = CookieManager(browser_session)
    logger.debug("CookieManager 实例创建成功")

    return cookie_manager


def get_global_model() -> TextRecognition:
    """获取全局模型实例"""
    global _global_model
    if _global_model is None:
        _global_model = TextRecognition(model_name="PP-OCRv5_server_rec")
    return _global_model


# 初始化自定义控制器
custom_controller = Controller(exclude_actions=["search_google"])


class SearchBaiduAction(BaseModel):
    query: str


@custom_controller.action(
    "Search the query in Baidu, the query should be a search query like humans search in Baidu, concrete and not vague or super long.",
    param_model=SearchBaiduAction,
)
async def search_baidu(params: SearchBaiduAction, browser_session: BrowserSession) -> ActionResult:
    search_url = f"https://www.baidu.com/s?wd={params.query}"

    page = await browser_session.get_current_page()
    if page.url.strip("/") == "https://www.baidu.com":
        # SECURITY FIX: Use browser_session.navigate_to() instead of direct page.goto()
        # This ensures URL validation against allowed_domains is performed
        await browser_session.navigate_to(search_url)
    else:
        # create_new_tab already includes proper URL validation
        page = await browser_session.create_new_tab(search_url)

    msg = f'🔍  Searched for "{params.query}" in Baidu'
    logger.info(msg)
    return ActionResult(
        extracted_content=msg, include_in_memory=True, long_term_memory=f"Searched Baidu for '{params.query}'"
    )


@custom_controller.action(
    "Batch set cookies using CDP session",
    param_model=SetCookiesBatchAction,
)
async def set_cookies_batch(params: SetCookiesBatchAction, browser_session: BrowserSession) -> ActionResult:
    """
    通过 CDP session 批量设置 cookie 值 - 使用原生 CDP 方法

    Args:
        params: 包含 cookie 列表的参数对象
        browser_session: 浏览器会话对象

    Returns:
        ActionResult: 操作结果

    Raises:
        RuntimeError: 当 CDP 操作失败时
    """
    try:
        logger.info(f"开始使用 CDP 批量设置 {len(params.cookies)} 个 cookies")

        # 创建新的 CookieManager 实例（每次调用都创建新实例）
        cookie_manager = create_cookie_manager(browser_session)

        # 使用 CookieManager 批量设置 cookies
        result = await cookie_manager.set_cookies_batch(params.cookies)

        # 构建结果消息
        total = result["total"]
        success = result["success"]
        failed = result["failed"]

        if failed > 0:
            failed_list = ", ".join(result["failed_cookies"])
            success_msg = f"批量设置 cookies 完成: {success}/{total} 成功，{failed} 个失败 ({failed_list})"
            logger.warning(success_msg)
        else:
            success_msg = f"成功使用 CDP 批量设置 {success} 个 cookies"
            logger.info(success_msg)

        # 构建长期记忆信息
        long_term_memory = f"使用 CDP 批量设置了 {success}/{total} 个 cookies"
        if failed > 0:
            long_term_memory += f"，{failed} 个失败"

        return ActionResult(
            extracted_content=success_msg,
            include_in_memory=True,
            long_term_memory=long_term_memory,
        )

    except Exception as e:
        error_msg = f"CDP 批量设置 cookies 失败: {str(e)}"
        logger.error(error_msg)
        raise RuntimeError(error_msg) from e


@custom_controller.action("login ecloud")
async def login_ecloud(username: str, password: str, page=None, is_script: bool = False) -> ActionResult:
    """
    登录ecloud

    Args:
        username: 用户名
        password: 密码
        page: 页面对象
        is_script: 是否是脚本登录
    Returns:
        ActionResult: 登录结果
    """
    logger.info(f"当前页面对象: {page}，是否是脚本登录: {is_script}")
    # Step 1: 导航到 ecloud 主页
    await page.goto("https://ecloud.10086.cn/portal")
    await page.wait_for_load_state("networkidle")
    logger.info(f"已导航到 ecloud 主页: {page.url}")

    # Step 2: 登录网站
    # 首先检查是否已经登录
    try:
        # 检查登录状态指示器
        login_indicator = page.locator("div[class*='has-login']")
        await login_indicator.wait_for(state="visible", timeout=3000)
        logger.info("检测到已登录状态，跳过登录操作")
        return ActionResult(extracted_content=f"用户 {username} 已处于登录状态")
    except Exception:
        logger.info("未检测到登录状态，开始执行登录流程")

        # 检查是否在登录页面
        current_url = page.url
        if "/login/" not in current_url and "login.html" not in current_url:
            # 不在登录页面，需要点击登录按钮
            logger.info("点击登录按钮进入登录页面")
            try:
                await page.get_by_text("登录", exact=True).click()
            except Exception:
                # 尝试其他方式找到登录按钮
                try:
                    await page.locator("a:has-text('登录')").first.click()
                    await page.wait_for_load_state("networkidle")
                except Exception as e:
                    logger.error(f"备用登录按钮点击也失败: {e}")
                    raise e

        # 确保选择账号登录标签
        try:
            await page.get_by_role("tab", name="账号登录").click()
        except Exception as e:
            logger.error(f"选择账号登录标签失败: {e}")
            raise e

        # 填写用户名（多种选择器尝试）
        try:
            await page.get_by_placeholder("账号名称/手机号码").fill(username)
        except Exception:
            try:
                await page.get_by_label("用户名").fill(username)
            except Exception:
                await page.locator("input[name='username']").fill(username)

        # 密码解密
        if not is_script:
            password = decrypt(password)
        # 填写密码（多种选择器尝试）
        try:
            await page.get_by_placeholder("密码").fill(password)
        except Exception:
            try:
                await page.get_by_label("密码").fill(password)
            except Exception:
                await page.locator("input[name='password']").fill(password)

        # 处理可能的协议复选框
        try:
            agreement_checkbox = page.locator("#form-protocol-account span").nth(1)
            if await agreement_checkbox.count() > 0 and not await agreement_checkbox.is_checked():
                await agreement_checkbox.check()
                logger.info("已勾选用户协议")
        except Exception as e:
            logger.info("未找到协议复选框或已勾选")
            raise e

        # 点击登录按钮
        try:
            await page.get_by_role("button", name="登录").click()
            logger.info("点击登录按钮")
        except Exception:
            try:
                await page.get_by_text("登录").click()
                logger.info("通过文本找到登录按钮并点击")
            except Exception as e:
                logger.error(f"登录按钮点击失败: {e}")
                raise e

        # 处理验证码
        have_captcha = False
        captcha_element = None
        try:
            captcha_element = page.locator(
                "#pane-account > div > div.el-dialog__wrapper > div > div.el-dialog__body > div > div > img"
            )
            await captcha_element.wait_for(state="visible", timeout=5000)
            have_captcha = True
        except Exception:
            logger.info("未找到验证码元素")
        # 如果找到验证码
        if have_captcha:
            try:
                logger.info("找到验证码元素")

                captcha_data = await captcha_element.screenshot()
                # 将 bytes 数据转换为 numpy array
                nparr = np.frombuffer(captcha_data, np.uint8)
                # 识别验证码
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                if image is None:
                    logger.error("无法解码验证码图片")
                    raise Exception("验证码图片解码失败")

                # 使用新的合并函数处理验证码，支持重试
                result = process_captcha_with_retry(image, max_retries=3)
                logger.info(f"验证码处理成功，结果: {result}")

                await page.get_by_placeholder("请输入验证码").fill(str(result))
                await page.get_by_role("button", name="确定").click()

            except Exception as e:
                logger.error(f"验证码处理失败: {e}")
                raise Exception(f"验证码处理失败: {e}") from e

        # 验证登录结果
        try:
            # 等待登录状态指示器出现
            await page.locator("div[class*='has-login']").wait_for(state="visible", timeout=10000)
            logger.info("登录成功，检测到登录状态指示器")
            await page.goto("about:blank")
            return ActionResult(extracted_content=f"用户 {username} 登录成功")
        except Exception as e:
            logger.info("登录状态验证超时，可能登录失败")
            raise e


def process_captcha_with_retry(image, max_retries=3):
    """
    处理验证码，包括识别和计算，支持重试

    Args:
        image: OpenCV 图像对象
        max_retries: 最大重试次数，默认3次

    Returns:
        int: 计算结果

    Raises:
        Exception: 当重试次数用完仍然失败时抛出异常
    """
    last_exception = None

    for attempt in range(max_retries):
        try:
            logger.info(f"验证码处理第 {attempt + 1} 次尝试")

            # 识别验证码
            model = get_global_model()
            result = model.predict(input=image, batch_size=1)

            if not result or not result[0]:
                raise Exception("OCR 未识别到任何文本")

            # 提取识别出的文本
            res = result[0]
            text = res.get("rec_text")
            score = res.get("rec_score")
            logger.info(f"识别文本: {text}, 置信度: {score}")
            if not text.strip():
                raise Exception("OCR 识别到空文本")

            logger.info(f"第 {attempt + 1} 次识别出的文本: {text}")

            # 步骤2: 计算表达式
            pattern = r"(\d+)([+\-*/])(\d+)"
            match = re.search(pattern, text)

            if not match:
                raise Exception(f"无法从文本中提取有效的数学表达式: {text}")

            num1 = int(match.group(1))
            operator = match.group(2)
            num2 = int(match.group(3))

            # 根据运算符进行计算
            if operator == "+":
                calc_result = num1 + num2
            elif operator == "-":
                calc_result = num1 - num2
            elif operator == "*":
                calc_result = num1 * num2
            elif operator == "/":
                if num2 == 0:
                    raise Exception("除零错误")
                calc_result = num1 // num2  # 使用整数除法
            else:
                raise Exception(f"不支持的运算符: {operator}")

            logger.info(f"第 {attempt + 1} 次计算结果: {calc_result}")
            return calc_result

        except Exception as e:
            last_exception = e
            logger.warning(f"第 {attempt + 1} 次验证码处理失败: {e}")

            # 如果还有重试机会，继续尝试
            if attempt < max_retries - 1:
                logger.info(f"准备进行第 {attempt + 2} 次尝试")
                continue

    # 所有重试都失败了
    logger.error(f"验证码处理失败，已重试 {max_retries} 次")
    raise Exception(f"验证码处理失败，已重试 {max_retries} 次。最后一次错误: {last_exception}")


def get_custom_controller() -> Controller:
    """
    获取自定义控制器实例

    Returns:
        Controller: 包含所有自定义函数的控制器
    """
    return custom_controller
