"""
RabbitMQ消息模型定义

定义数据库操作消息的结构和类型
"""

from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field

from app.config.enums import ExecutionStatus
from app.utils.timezone import get_shanghai_now


class DatabaseOperationType(str, Enum):
    """数据库操作类型枚举"""

    UPDATE_STATUS = "update_status"  # 更新任务状态
    APPEND_LOG = "append_log"  # 追加步骤日志
    UPDATE_RESULT = "update_result"  # 更新执行结果
    UPDATE_ERROR = "update_error"  # 更新错误信息
    UPDATE_SELENIUM_SESSION = "update_selenium_session"  # 更新Selenium会话
    UPDATE_FILE_PATHS = "update_file_paths"  # 更新文件路径
    APPEND_SCREENSHOT_PATH = "append_screenshot_path"  # 追加截图路径
    APPEND_VIDEO_PATH = "append_video_path"  # 追加视频路径
    APPEND_FILE_PATH = "append_file_path"  # 追加下载文件路径


class DatabaseMessage(BaseModel):
    """数据库操作消息"""

    # 消息ID，用于幂等性控制
    message_id: str = Field(..., description="消息唯一标识")

    # 操作类型
    operation_type: DatabaseOperationType = Field(..., description="数据库操作类型")

    # 任务ID
    ticket_id: str = Field(..., description="任务ID")

    # 操作数据，根据操作类型有不同的结构
    data: dict = Field(..., description="操作数据")

    # 消息创建时间
    created_at: datetime = Field(default_factory=lambda: get_shanghai_now(), description="消息创建时间")

    # 重试次数
    retry_count: int = Field(default=0, description="当前重试次数")

    # 最大重试次数
    max_retry: int = Field(default=3, description="最大重试次数")


class UpdateStatusMessage(BaseModel):
    """更新状态消息数据"""

    status: ExecutionStatus = Field(..., description="任务状态")
    result_or_error: Any | None = Field(None, description="结果或错误信息")
    step_log_message: str | None = Field(None, description="步骤日志消息")


class UpdateSeleniumSessionMessage(BaseModel):
    """Selenium会话消息数据"""

    session_id: str = Field(..., description="Selenium会话ID")


class AppendLogMessage(BaseModel):
    """追加日志消息数据"""

    message: str = Field(..., description="日志消息")
    append: bool = Field(default=True, description="是否追加")


class UpdateResultMessage(BaseModel):
    """更新结果消息数据"""

    result: Any = Field(..., description="执行结果")


class UpdateErrorMessage(BaseModel):
    """更新错误消息数据"""

    error_message: str = Field(..., description="错误信息")


class UpdateFilePathsMessage(BaseModel):
    """更新文件路径消息数据"""

    video_file_paths: list[str] | None = Field(None, description="视频文件路径列表")
    screenshot_file_paths: list[str] | None = Field(None, description="截图文件路径列表")
    download_file_paths: list[str] | None = Field(None, description="下载文件路径列表")


class AppendScreenshotPathMessage(BaseModel):
    """追加截图路径消息数据"""

    screenshot_path: str = Field(..., description="截图文件路径")


class AppendVideoPathMessage(BaseModel):
    """追加视频路径消息数据"""

    video_path: str = Field(..., description="视频文件路径")


class AppendFilePathMessage(BaseModel):
    """追加下载文件路径消息数据"""

    file_path: str = Field(..., description="下载文件路径")


class MessageResponse(BaseModel):
    """消息发送响应"""

    success: bool = Field(..., description="是否成功")
    message_id: str | None = Field(None, description="消息ID")
    error: str | None = Field(None, description="错误信息")
