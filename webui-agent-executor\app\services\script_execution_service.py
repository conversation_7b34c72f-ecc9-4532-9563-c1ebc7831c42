"""
脚本执行服务

负责脚本的执行、进程管理、监控等执行逻辑
"""

import asyncio
import os
import tempfile
from pathlib import Path

from app.config.enums import ExecutionStatus
from app.config.settings import settings
from app.models.script import Script
from app.schemas.script import ScriptTerminateResponse
from app.services.callback_service import get_callback_service
from app.utils.crypto import decrypt
from app.utils.log import logger
from app.utils.selenium_grid import disconnect_from_selenium, get_selenium_cdp_url
from app.utils.timezone import get_shanghai_now, to_shanghai_tz

# 存储脚本进程的全局字典，用于跟踪正在运行的脚本进程
script_processes: dict[str, asyncio.subprocess.Process] = {}


async def terminate_process(process):
    """终止指定的脚本进程"""
    try:
        process.terminate()

        # 等待进程响应 SIGTERM，最多等待5秒
        try:
            await asyncio.wait_for(process.wait(), timeout=5.0)
            return
        except TimeoutError:
            # 如果超时，强制杀死进程
            process.kill()
            try:
                await asyncio.wait_for(process.wait(), timeout=3.0)
            except TimeoutError:
                logger.warning("脚本进程无法终止")

    except Exception as e:
        logger.debug(f"终止脚本进程时发生异常: {str(e)}")


async def monitor_script_process(ticket_id: str, process):
    """
    脚本进程监控 - 负责超时和取消的检查
    """
    try:
        while True:
            # 检查进程是否还在运行
            try:
                # 非阻塞地检查进程状态
                return_code = process.returncode
                if return_code is not None:
                    # 进程已结束
                    logger.debug(f"监控脚本 {ticket_id} - 进程已结束，返回码: {return_code}")
                    break
            except Exception as poll_error:
                logger.warning(f"监控脚本 {ticket_id} - 检查进程状态失败: {poll_error}")
                break
            logger.debug(f"监控脚本 {ticket_id} - 进程仍在运行")
            script = await Script.get_or_none(ticket_id=ticket_id)
            if not script:
                logger.warning(f"监控脚本 {ticket_id} - 数据库中未找到脚本记录，停止监控")
                break

            # 检查是否被取消
            if script.status == ExecutionStatus.CANCELLED:
                logger.info(f"监控脚本 {ticket_id} - 脚本已被取消，正在终止进程")
                await terminate_process(process)
                break

            # 检查是否超时
            if script.started_at and settings.script_timeout_seconds > 0:
                now = get_shanghai_now()
                script_started = to_shanghai_tz(script.started_at)
                elapsed = now - script_started
                if elapsed.total_seconds() > settings.script_timeout_seconds:
                    logger.warning(f"监控脚本 {ticket_id} - 脚本执行超时，已运行 {elapsed.total_seconds():.1f} 秒")

                    # 标记脚本为超时失败
                    await Script.filter(ticket_id=ticket_id).update(
                        status=ExecutionStatus.FAILED,
                        stderr=f"脚本执行超时（超过{settings.script_timeout_seconds}秒）",
                        return_code=-1,
                        completed_at=now,
                    )

                    await terminate_process(process)
                    break

            await asyncio.sleep(settings.monitor_check_interval)

        logger.info(f"监控脚本 {ticket_id} - 进程已结束，监控完成")

    except Exception as e:
        logger.error(f"监控脚本时发生异常: {ticket_id}, {str(e)}")
        # 在监控异常时，尝试将脚本标记为失败
        try:
            await Script.filter(ticket_id=ticket_id).update(
                status=ExecutionStatus.FAILED,
                stderr=f"监控脚本进程时出错: {str(e)}",
                return_code=-1,
                completed_at=get_shanghai_now(),
            )
        except Exception as update_error:
            logger.error(f"更新脚本状态失败: {ticket_id}, {str(update_error)}")
    finally:
        # 释放Selenium会话（如果存在）
        script = await Script.get_or_none(ticket_id=ticket_id)
        if script and script.selenium_session_id and settings.selenium_remote_url:
            disconnect_from_selenium(settings.selenium_remote_url, script.selenium_session_id)

        # 清理本地进程记录
        if ticket_id in script_processes:
            del script_processes[ticket_id]
            logger.debug(f"清理本地脚本进程记录: {ticket_id}")


async def terminate_script_process(ticket_id: str) -> ScriptTerminateResponse:
    """
    终止脚本执行（支持多副本环境）

    Args:
        ticket_id: 脚本ID

    Returns:
        ScriptTerminateResponse: 终止操作结果
    """
    try:
        # 获取脚本记录
        script = await Script.get_or_none(ticket_id=ticket_id)
        if not script:
            logger.warning(f"脚本不存在: {ticket_id}")
            return ScriptTerminateResponse(ticket_id=ticket_id, message="脚本不存在", success=False)

        # 检查脚本状态，只有 PENDING 或 RUNNING 状态的脚本可以被终止
        if script.status not in [ExecutionStatus.PENDING, ExecutionStatus.RUNNING]:
            logger.info(f"脚本已完成，无法终止: {ticket_id}, 当前状态: {script.status}")
            return ScriptTerminateResponse(
                ticket_id=ticket_id, message=f"脚本已完成，当前状态为 {script.status}，无法终止", success=False
            )

        # 释放 Selenium 会话资源
        if script.selenium_session_id and settings.selenium_remote_url:
            disconnect_from_selenium(settings.selenium_remote_url, script.selenium_session_id)

        # 首先标记脚本为取消状态（这样其他副本的监控进程能够看到）
        await Script.filter(ticket_id=ticket_id).update(
            status="CANCELLED", stderr="脚本执行被手动终止", return_code=-1, completed_at=get_shanghai_now()
        )

        # 检查脚本是否在当前副本运行
        if script.replica_id == settings.replica_id and ticket_id in script_processes:
            # 脚本在当前副本运行，直接终止本地进程
            process = script_processes[ticket_id]
            try:
                await terminate_process(process)
                logger.info(f"脚本进程已终止: {ticket_id}")
            except Exception as process_error:
                logger.error(f"终止脚本进程失败: {ticket_id}, 错误: {process_error}")
            finally:
                # 清理进程记录
                if ticket_id in script_processes:
                    del script_processes[ticket_id]

            logger.info(f"脚本已成功终止（本地进程）: {ticket_id}")
            return ScriptTerminateResponse(
                ticket_id=ticket_id,
                message="脚本已成功终止（本地进程）",
                success=True,
            )

        elif script.replica_id and script.replica_id != settings.replica_id:
            # 脚本在其他副本运行，只标记为取消状态，让对应副本的监控进程处理
            logger.info(f"脚本在远程副本 {script.replica_id} 运行，仅标记为取消: {ticket_id}")
            return ScriptTerminateResponse(
                ticket_id=ticket_id,
                message=f"脚本在副本 {script.replica_id} 运行，已标记为取消，由对应副本处理终止",
                success=True,
            )
        else:
            # 脚本无副本信息或其他情况，仅标记为取消
            logger.info(f"脚本无副本信息，仅标记为取消: {ticket_id}")
            return ScriptTerminateResponse(
                ticket_id=ticket_id,
                message="脚本已标记为取消",
                success=True,
            )

    except Exception as e:
        logger.error(f"终止脚本失败: {ticket_id}, 错误: {e}")
        return ScriptTerminateResponse(ticket_id=ticket_id, message=f"终止脚本失败: {str(e)}", success=False)


async def execute_script(ticket_id: str, username: str | None = None, password: str | None = None) -> None:
    """
    执行脚本

    Args:
        ticket_id: 脚本ID
        username: 可选的用户名
        password: 可选的密码
    """
    selenium_session_id = None
    actual_cdp_url = None
    process = None

    try:
        # 获取脚本记录
        script = await Script.get(ticket_id=ticket_id)

        # 自动获取Selenium会话
        if settings.selenium_remote_url:
            try:
                logger.info(f"获取Selenium会话: ticket_id={ticket_id}")
                selenium_session = get_selenium_cdp_url(settings.selenium_remote_url, browser_name="chrome")
                actual_cdp_url = selenium_session["cdp_url"]
                selenium_session_id = selenium_session["session_id"]
                logger.info(f"获取到Selenium会话: session_id={selenium_session_id}, cdp_url={actual_cdp_url}")
            except Exception as selenium_error:
                logger.error(f"获取Selenium会话失败: {selenium_error}")
                # 如果CDP URL获取失败，直接设置为FAILURE状态
                script.status = ExecutionStatus.FAILED
                script.stderr = f"获取Selenium会话失败: {str(selenium_error)}"
                script.return_code = -1
                script.completed_at = get_shanghai_now()
                await script.save()
                return

        # 更新状态为运行中
        script.status = ExecutionStatus.RUNNING
        script.started_at = get_shanghai_now()
        script.replica_id = settings.replica_id
        script.selenium_session_id = str(selenium_session_id)
        await script.save()

        logger.info(f"开始执行脚本: ticket_id={ticket_id}, selenium_session_id={selenium_session_id}")

        # 创建临时文件在项目根目录
        repo_root = Path(__file__).parent.parent.parent  # 获取repository root
        temp_file = None

        try:
            # 在repository root创建临时文件
            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".py", delete=False, dir=repo_root, encoding="utf-8"
            ) as f:
                f.write(script.script_content)
                temp_file = f.name

            logger.info(f"创建临时脚本文件: {temp_file}")

            # 构建命令
            temp_file_path = Path(temp_file)
            relative_path = temp_file_path.relative_to(repo_root)

            # 根据用户名、密码和CDP URL构建命令参数
            cmd = ["uv", "run", str(relative_path)]

            # 添加用户名和密码参数
            if username and password:
                cmd.extend(["--username", username, "--password", decrypt(password)])
            else:
                cmd.extend(["--username", "", "--password", ""])

            # 添加CDP URL参数（如果有）
            if actual_cdp_url:
                cmd.extend(["--cdp_url", actual_cdp_url])

            # 执行脚本
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=repo_root,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=os.environ.copy(),
            )

            # 将进程添加到全局跟踪字典
            script_processes[ticket_id] = process

            # 启动监控任务（不等待完成）
            asyncio.create_task(monitor_script_process(ticket_id, process))

            try:
                stdout, stderr = await process.communicate()

                return_code = process.returncode
                stdout_text = stdout.decode("utf-8", errors="replace") if stdout else ""
                stderr_text = stderr.decode("utf-8", errors="replace") if stderr else ""

                logger.info(f"脚本执行完成: ticket_id={ticket_id}, return_code={return_code}")

                # 检查脚本是否被手动取消
                current_script = await Script.get(ticket_id=ticket_id)
                if current_script.status == ExecutionStatus.CANCELLED:
                    logger.info(f"脚本已被手动取消: ticket_id={ticket_id}")
                    return  # 不更新状态，保持CANCELLED状态

                # 更新执行结果
                script.status = ExecutionStatus.SUCCESS if return_code == 0 else ExecutionStatus.FAILED
                script.stdout = stdout_text
                script.stderr = stderr_text
                script.return_code = int(return_code)
                script.completed_at = get_shanghai_now()
                await script.save()
                try:
                    # 回调结果
                    callback_service = get_callback_service()
                    callback_kwargs = {"type": "script", "ticket_id": ticket_id, "status": script.status.value}
                    callback_success = callback_service.send_task_callback_sync(**callback_kwargs)
                    if callback_success:
                        logger.info(f"脚本执行结果回调发送成功: {ticket_id} -> {script.status}")
                    else:
                        logger.warning(f"脚本执行结果回调发送失败: {ticket_id} -> {script.status} (不影响脚本执行)")
                except Exception as callback_error:
                    logger.warning(f"发送脚本回调时发生异常: {ticket_id}, 错误: {str(callback_error)} (不影响脚本执行)")

            except Exception as execution_error:
                logger.error(f"脚本执行过程中发生异常: ticket_id={ticket_id}, 错误: {execution_error}")

                # 检查脚本是否被手动取消
                current_script = await Script.get(ticket_id=ticket_id)
                if current_script.status == ExecutionStatus.CANCELLED:
                    logger.info(f"脚本已被手动取消: ticket_id={ticket_id}")
                    return  # 不更新状态，保持CANCELLED状态

                # 更新状态为失败
                script.status = ExecutionStatus.FAILED
                script.stderr = f"脚本执行异常: {str(execution_error)}"
                script.return_code = -1
                script.completed_at = get_shanghai_now()
                await script.save()

        finally:
            # 清理进程记录
            if ticket_id in script_processes:
                del script_processes[ticket_id]

            # 清理临时文件
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                    logger.info(f"清理临时文件: {temp_file}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {temp_file}, 错误: {e}")

    except Exception as e:
        logger.error(f"脚本执行异常: ticket_id={ticket_id}, 错误: {e}")

        # 清理进程记录
        if ticket_id in script_processes:
            del script_processes[ticket_id]

        # 更新状态为失败
        try:
            script = await Script.get(ticket_id=ticket_id)
            # 检查脚本是否被手动取消
            if script.status == ExecutionStatus.CANCELLED:
                logger.info(f"脚本已被手动取消: ticket_id={ticket_id}")
                return  # 不更新状态，保持CANCELLED状态

            script.status = ExecutionStatus.FAILED
            script.stderr = f"脚本执行异常: {str(e)}"
            script.return_code = -1
            script.completed_at = get_shanghai_now()
            await script.save()
        except Exception as save_error:
            logger.error(f"保存脚本执行异常状态失败: {save_error}")
