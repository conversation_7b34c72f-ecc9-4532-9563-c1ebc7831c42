"""
Browser Use FastAPI 多副本服务主应用

这是重构后的主应用文件，采用了更清晰的项目结构
"""

from contextlib import asynccontextmanager

from dotenv import load_dotenv

# 首先加载环境变量
load_dotenv(override=True)

from fastapi import FastAPI

from app.api.dependencies import init_database
from app.api.routes.script import router as script_router
from app.api.routes.task import router as task_router
from app.config.settings import settings
from app.services.mq_consumer import start_global_consumer, stop_global_consumer
from app.services.script_execution_service import script_processes, terminate_script_process
from app.services.task_execution_service import local_processes, terminate_task_process
from app.utils.log import logger


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("启动 RabbitMQ 消费者...")
    try:
        start_global_consumer()
        logger.info("RabbitMQ 消费者启动成功")
    except Exception as e:
        logger.error(f"启动 RabbitMQ 消费者失败: {str(e)}")
        raise

    yield

    # 关闭所有任务进程
    logger.info("关闭所有任务进程")
    local_processes_keys = list(local_processes.keys())
    for ticket_id in local_processes_keys:
        await terminate_task_process(ticket_id)

    # 关闭所有脚本进程
    logger.info("关闭所有脚本进程")
    script_processes_keys = list(script_processes.keys())
    for ticket_id in script_processes_keys:
        await terminate_script_process(ticket_id)

    # 关闭时的清理
    logger.info("关闭 RabbitMQ 消费者...")
    try:
        stop_global_consumer()
        logger.info("RabbitMQ 消费者关闭成功")
    except Exception as e:
        logger.error(f"关闭 RabbitMQ 消费者失败: {str(e)}")


# 记录应用启动
logger.info("初始化 FastAPI 应用")

# 创建 FastAPI 应用实例
app = FastAPI(
    title=settings.app_title,
    version=settings.app_version,
    description="WebUI智能体执行服务",
    lifespan=lifespan,
    openapi_url=settings.openapi_url,
)

# 初始化数据库
init_database(app)

# 注册路由
app.include_router(task_router, prefix="/v1/task", tags=["task"])
app.include_router(script_router, prefix="/v1/script", tags=["script"])

logger.info(f"应用初始化完成 - 副本ID: {settings.replica_id}")


@app.get("/")
async def root():
    """根路径，返回服务信息"""
    return {
        "service": settings.app_title,
        "version": settings.app_version,
        "replica_id": settings.replica_id,
        "status": "running",
    }


@app.get("/health")
async def health():
    """健康检查接口"""
    return {"status": "healthy", "replica_id": settings.replica_id}


if __name__ == "__main__":
    import uvicorn

    # 启动 FastAPI 服务
    uvicorn.run(app, host=settings.host, port=settings.port)
