2025-07-25 10:59:44 | ERROR    | T36136 | app.services.process_service - 任务执行异常: c2458b4edb7f43d886ffcb8127c9853c, Agent 执行异常: HTTPSConnectionPool(host='openaipublic.blob.core.windows.net', port=443): Max retries exceeded with url: /encodings/cl100k_base.tiktoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001BD4692F380>, 'Connection to openaipublic.blob.core.windows.net timed out. (connect timeout=None)'))
2025-07-25 12:23:55 | ERROR    | T50568 | __main__ - ❌ 发送测试消息失败: Object of type datetime is not JSON serializable
2025-07-25 12:23:58 | ERROR    | T50568 | __main__ - ❌ 发送测试消息失败: Object of type datetime is not JSON serializable
2025-07-25 12:24:03 | ERROR    | T50568 | __main__ - ❌ 发送测试消息失败: Object of type datetime is not JSON serializable
2025-07-25 15:31:31 | ERROR    | T42380 | logging - Traceback (most recent call last):
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\asyncpg\client.py", line 89, in _translate_exceptions
    return await func(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\base_postgres\client.py", line 156, in execute_script
    await connection.execute(query)
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\asyncpg\connection.py", line 349, in execute
    result = await self._protocol.query(query, timeout)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "asyncpg\\protocol\\protocol.pyx", line 375, in query
asyncpg.exceptions.UndefinedColumnError: column "video_file_path" of relation "task" does not exist

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\routing.py", line 692, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
               ^^^^^^^^^^^^^^^^^^^^^
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
               ^^^^^^^^^^^^^^^^^^^^^
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\contrib\fastapi\__init__.py", line 271, in orm_lifespan
    async with RegisterTortoise(
               ^^^^^^^^^^^^^^^^^
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\contrib\fastapi\__init__.py", line 177, in __aenter__
    await self.init_orm()
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\contrib\fastapi\__init__.py", line 166, in init_orm
    await Tortoise.generate_schemas()
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\__init__.py", line 594, in generate_schemas
    await generate_schema_for_client(connection, safe)
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\utils.py", line 46, in generate_schema_for_client
    await generator.generate_from_string(schema)
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\base\schema_generator.py", line 504, in generate_from_string
    await self.client.execute_script(creation_string)
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\base_postgres\client.py", line 33, in _translate_exceptions
    return await self._translate_exceptions(func, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\asyncpg\client.py", line 91, in _translate_exceptions
    raise OperationalError(exc)
tortoise.exceptions.OperationalError: column "video_file_path" of relation "task" does not exist

2025-07-25 15:31:31 | ERROR    | T42380 | logging - Application startup failed. Exiting.
2025-07-29 18:42:01 | ERROR    | T14704 | app.services.callback_service - 任务回调处理失败: c315d6115f324926b620c6cc4584aac2, 消息: WebUI任务回调处理成功
2025-07-29 18:42:01 | ERROR    | T14704 | app.services.mq_consumer - 任务状态回调发送失败: c315d6115f324926b620c6cc4584aac2 -> SUCCESS
2025-07-29 18:56:39 | ERROR    | T46748 | app.utils.selenium_grid - [第1次] Selenium Grid会话请求异常: timed out
2025-07-29 18:57:12 | ERROR    | T46748 | app.utils.selenium_grid - [第2次] Selenium Grid会话请求异常: timed out
2025-07-29 18:57:44 | ERROR    | T46748 | app.utils.selenium_grid - [第3次] Selenium Grid会话请求异常: timed out
2025-07-29 18:57:44 | ERROR    | T46748 | app.services.process_service - 任务执行异常: 23b411fab78f4b9fa7235df0d7c51acd, Agent 执行异常: Selenium Grid会话请求失败，重试3次后仍未成功: timed out
2025-07-31 16:01:26 | ERROR    | T50084 | logging - Exception in ASGI application

Traceback (most recent call last):

  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\asyncpg\client.py", line 89, in _translate_exceptions
    return await func(self, *args, **kwargs)
                 │    │      │       └ {}
                 │    │      └ ('INSERT INTO "task" ("ticket_id","status","task_description","step_log","result","error_message","replica_id","selenium_sess...
                 │    └ <tortoise.backends.asyncpg.client.AsyncpgDBClient object at 0x000002CF93E3A960>
                 └ <function AsyncpgDBClient.execute_insert at 0x000002CF93FD0860>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\asyncpg\client.py", line 112, in execute_insert
    return await connection.fetchrow(query, *values)
                 │          │        │       └ ['02d401ad284a4732ac11cac6ad26f1aa', 'PENDING', '打开百度，然后结束任务', None, None, None, 'XIAOXIN-PRO14', None, datetime.datetime(202...
                 │          │        └ 'INSERT INTO "task" ("ticket_id","status","task_description","step_log","result","error_message","replica_id","selenium_sessi...
                 │          └ <function Connection.fetchrow at 0x000002CF93FA5E40>
                 └ <PoolConnectionProxy [released] 0x2cf93ed0160>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\asyncpg\connection.py", line 748, in fetchrow
    data = await self._execute(
                 │    └ <function Connection._execute at 0x000002CF93F9A660>
                 └ <asyncpg.connection.Connection object at 0x000002CF93FC2990>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\asyncpg\connection.py", line 1864, in _execute
    result, _ = await self.__execute(
                      └ <asyncpg.connection.Connection object at 0x000002CF93FC2990>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\asyncpg\connection.py", line 1961, in __execute
    result, stmt = await self._do_execute(
                         │    └ <function Connection._do_execute at 0x000002CF93F9AAC0>
                         └ <asyncpg.connection.Connection object at 0x000002CF93FC2990>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\asyncpg\connection.py", line 2004, in _do_execute
    stmt = await self._get_statement(
                 │    └ <function Connection._get_statement at 0x000002CF93F987C0>
                 └ <asyncpg.connection.Connection object at 0x000002CF93FC2990>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\asyncpg\connection.py", line 432, in _get_statement
    statement = await self._protocol.prepare(
                      │    └ <member '_protocol' of 'Connection' objects>
                      └ <asyncpg.connection.Connection object at 0x000002CF93FC2990>
  File "asyncpg\\protocol\\protocol.pyx", line 165, in prepare
    return await waiter

asyncpg.exceptions.UndefinedTableError: relation "task" does not exist


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "Z:\code\AINT_webui_agent\webui-agent-executor\main.py", line 19, in <module>
    uvicorn.run(
    │       └ <function run at 0x000002CFCBC97240>
    └ <module 'uvicorn' from 'Z:\\code\\AINT_webui_agent\\webui-agent-executor\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002CFCBC69260>
    └ <uvicorn.server.Server object at 0x000002CF93B702F0>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002CFCBC69300>
           │       │   └ <uvicorn.server.Server object at 0x000002CF93B702F0>
           │       └ <function run at 0x000002CFC59D0680>
           └ <module 'asyncio' from 'Z:\\packages\\uv\\python-install\\cpython-3.12.9-windows-x86_64-none\\Lib\\asyncio\\__init__.py'>
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002CF93DEF920>
           │      └ <function Runner.run at 0x000002CFC7B6FBA0>
           └ <asyncio.runners.Runner object at 0x000002CF93C00470>
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-pack...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002CFC7B6D6C0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002CF93C00470>
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002CFC7FD2B60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002CFC7B6F420>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002CFC7AE71A0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "Z:\packages\uv\python-install\cpython-3.12.9-windows-x86_64-none\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002CF93D232C0>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7EC0>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002CFFFD2D100>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002CF93D232C0>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7EC0>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7EC0>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002CF93ED00B0>
          └ <fastapi.applications.FastAPI object at 0x000002CFFFD2D100>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\middleware\errors.py", line 187, in __call__
    raise exc
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002CF93FD2020>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002CF93EABE90>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002CF93ED00B0>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002CF93FD2020>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002CF93FC7B30>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002CF9380BFB0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002CF93EABE90>
          └ <function wrap_app_handling_exceptions at 0x000002CFC8F26480>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002CF93FD2DE0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002CF9380BFB0>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002CF93FD2DE0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002CF9380BFB0>>
          └ <fastapi.routing.APIRouter object at 0x000002CF9380BFB0>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002CF93FD2DE0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
          │     └ <function Route.handle at 0x000002CFC8F279C0>
          └ APIRoute(path='/v1/task/execute', name='execute', methods=['POST'])
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002CF93FD2DE0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
          │    └ <function request_response.<locals>.app at 0x000002CF93E70900>
          └ APIRoute(path='/v1/task/execute', name='execute', methods=['POST'])
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002CF93FD2DE0>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002CF93FC7470>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002CF93FD2E80>
          └ <function wrap_app_handling_exceptions at 0x000002CFC8F26480>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002CF93FD2FC0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002CF93FC7...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8080), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002CF93FD2E80>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000002CF93FC7470>
                     └ <function get_request_handler.<locals>.app at 0x000002CF93E70A40>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000002CFC8F25DA0>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': TaskExecuteRequest(task='打开百度，然后结束任务', username=None, password=None, provider_type=None)}
                 │         └ <function execute at 0x000002CF93D62F20>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "Z:\code\AINT_webui_agent\webui-agent-executor\app\api\routes\task.py", line 40, in execute
    ticket_id = await create_task(request.task)
                      │           │       └ '打开百度，然后结束任务'
                      │           └ TaskExecuteRequest(task='打开百度，然后结束任务', username=None, password=None, provider_type=None)
                      └ <function create_task at 0x000002CF93DCDA80>

  File "Z:\code\AINT_webui_agent\webui-agent-executor\app\services\task_service.py", line 34, in create_task
    await Task.create(
          │    └ <classmethod(<function Model.create at 0x000002CFC91C4040>)>
          └ <class 'app.models.task.Task'>

  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\models.py", line 1210, in create
    await instance.save(using_db=db, force_create=True)
          │        │             └ <tortoise.backends.asyncpg.client.AsyncpgDBClient object at 0x000002CF93E3A960>
          │        └ <function Model.save at 0x000002CFC91BB9C0>
          └ <Task>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\models.py", line 996, in save
    await executor.execute_insert(self)
          │        │              └ <Task>
          │        └ <function BaseExecutor.execute_insert at 0x000002CFC9161DA0>
          └ <tortoise.backends.asyncpg.executor.AsyncpgExecutor object at 0x000002CF93F00D40>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\base\executor.py", line 188, in execute_insert
    insert_result = await self.db.execute_insert(self.insert_query, values)
                          │    │  │              │    │             └ ['02d401ad284a4732ac11cac6ad26f1aa', 'PENDING', '打开百度，然后结束任务', None, None, None, 'XIAOXIN-PRO14', None, datetime.datetime(202...
                          │    │  │              │    └ 'INSERT INTO "task" ("ticket_id","status","task_description","step_log","result","error_message","replica_id","selenium_sessi...
                          │    │  │              └ <tortoise.backends.asyncpg.executor.AsyncpgExecutor object at 0x000002CF93F00D40>
                          │    │  └ <function AsyncpgDBClient.execute_insert at 0x000002CF93FD0900>
                          │    └ <tortoise.backends.asyncpg.client.AsyncpgDBClient object at 0x000002CF93E3A960>
                          └ <tortoise.backends.asyncpg.executor.AsyncpgExecutor object at 0x000002CF93F00D40>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\base_postgres\client.py", line 33, in _translate_exceptions
    return await self._translate_exceptions(func, *args, **kwargs)
                 │    │                     │      │       └ {}
                 │    │                     │      └ ('INSERT INTO "task" ("ticket_id","status","task_description","step_log","result","error_message","replica_id","selenium_sess...
                 │    │                     └ <function AsyncpgDBClient.execute_insert at 0x000002CF93FD0860>
                 │    └ <function AsyncpgDBClient._translate_exceptions at 0x000002CF93FD0680>
                 └ <tortoise.backends.asyncpg.client.AsyncpgDBClient object at 0x000002CF93E3A960>
  File "Z:\code\AINT_webui_agent\webui-agent-executor\.venv\Lib\site-packages\tortoise\backends\asyncpg\client.py", line 91, in _translate_exceptions
    raise OperationalError(exc)
          └ <class 'tortoise.exceptions.OperationalError'>

tortoise.exceptions.OperationalError: relation "task" does not exist
2025-07-31 16:02:20 | ERROR    | T31908 | logging - ❌ Result failed 1/3 times:
 ('MindIE-MS Coordinator is not ready', 502)
2025-08-01 14:10:21 | ERROR    | T26932 | app.utils.custom_actions - 验证码处理失败，已重试 3 次
2025-08-01 14:10:21 | ERROR    | T26932 | app.utils.custom_actions - 验证码处理失败: 验证码处理失败，已重试 3 次。最后一次错误: 无法从文本中提取有效的数学表达式: 459=?
