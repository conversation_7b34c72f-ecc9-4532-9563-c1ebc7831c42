"""
任务相关的 API 路由

包含所有任务管理的 HTTP 接口
"""

from fastapi import APIRouter

from app.schemas.task import (
    TaskExecuteRequest,
    TaskExecuteResponse,
    TaskFilesResponse,
    TaskResultResponse,
    TaskStatusResponse,
    TaskTerminateResponse,
)
from app.services.task_execution_service import start_agent_process, terminate_task_process
from app.services.task_service import (
    create_task,
    get_task_files,
    get_task_result,
    get_task_status,
)

router = APIRouter()


@router.post("/execute", response_model=TaskExecuteResponse)
async def execute(request: TaskExecuteRequest):
    """
    创建新的浏览器自动化任务

    流程：
    1. 生成唯一的任务ID
    2. 在数据库中创建任务记录
    3. 启动进程执行任务
    4. 立即返回任务ID
    """
    # 创建任务记录
    ticket_id = await create_task(request.task)

    # 启动任务进程，传递可选的登录凭据、云服务提供商类型和 cookies
    await start_agent_process(
        ticket_id, request.task, request.username, request.password, request.provider_type, request.cookies
    )

    return TaskExecuteResponse(ticket_id=ticket_id, status="PENDING", message="任务已创建并开始执行")


@router.get("/result/{ticket_id}", response_model=TaskResultResponse)
async def get_result(ticket_id: str):
    """
    获取任务的详细结果

    返回完整的任务信息，包括：
    - 任务状态
    - 任务执行步骤日志
    - 执行结果或错误信息
    - 执行副本信息
    - 时间戳
    """
    return await get_task_result(ticket_id)


@router.get("/status/{ticket_id}", response_model=TaskStatusResponse)
async def get_status(ticket_id: str):
    """
    获取任务状态（轻量级接口）

    只返回基本的状态信息，不包含结果数据
    适合频繁轮询使用
    """
    return await get_task_status(ticket_id)


@router.get("/terminate/{ticket_id}", response_model=TaskTerminateResponse)
async def terminate(ticket_id: str):
    """
    终止指定的任务

    跨副本终止机制：
    1. 本地任务：直接终止进程
    2. 远程任务：标记为取消状态，依赖其他副本检查
    3. 已结束任务：仅更新状态

    使用两阶段终止：SIGTERM -> SIGKILL
    """
    success, message = await terminate_task_process(ticket_id)
    return TaskTerminateResponse(ticket_id=ticket_id, message=message, success=success)


@router.get("/files/{ticket_id}", response_model=TaskFilesResponse)
async def get_files(ticket_id: str):
    """
    获取任务的所有文件URL列表

    根据任务ID获取该任务的所有文件URL，返回三个独立的URL列表：
    - video_urls: 视频文件URL列表（通常只有一个元素或为空）
    - screenshots_urls: 截图文件URL列表（按时间顺序排列）
    - file_urls: 下载文件URL列表（按下载顺序排列）

    数据来源：
    - 从数据库Task模型的 video_file_path、screenshot_file_paths、download_file_paths 字段获取
    - 直接返回数据库中存储的文件路径作为URL，不进行文件验证或预签名处理

    响应字段：
    - ticket_id: 任务ID
    - video_urls: 视频文件URL列表
    - screenshots_urls: 截图文件URL列表
    - file_urls: 下载文件URL列表

    Args:
        ticket_id: 任务ID

    Returns:
        TaskFilesResponse: 包含三个URL列表的任务文件响应

    Raises:
        HTTPException: 任务不存在时返回404错误
    """
    return await get_task_files(ticket_id)
