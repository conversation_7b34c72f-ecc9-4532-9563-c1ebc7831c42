<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c423dfa9-699b-4e95-9f0b-6ce23d66f1d0" name="Changes" comment="UI测试脚本支持执行&#10;&#10;Code Source From: Self Code&#10;Description:  [Optional]&#10;Jira:  #AIDT-62&#10;市场项目编号（名称）：[Optional]" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30Y0K4OiK1a5mj0Q2g7X1gq66n8" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;feature__YDYCMKK-982&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c423dfa9-699b-4e95-9f0b-6ce23d66f1d0" name="Changes" comment="" />
      <created>1753789131664</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753789131664</updated>
    </task>
    <task id="LOCAL-00001" summary="优化selenium grid连接管理&#10;&#10;Code Source From: Self Code&#10;Description:  [Optional]&#10;Jira:  #AIDT-60&#10;市场项目编号（名称）：[Optional]">
      <option name="closed" value="true" />
      <created>1753842128918</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753842128918</updated>
    </task>
    <task id="LOCAL-00002" summary="优化回调函数&#10;&#10;Code Source From: Self Code&#10;Description:  [Optional]&#10;Jira:  #AIDT-60&#10;市场项目编号（名称）：[Optional]">
      <option name="closed" value="true" />
      <created>1753863689427</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753863689427</updated>
    </task>
    <task id="LOCAL-00003" summary="UI测试脚本支持执行&#10;&#10;Code Source From: Self Code&#10;Description:  [Optional]&#10;Jira:  #AIDT-62&#10;市场项目编号（名称）：[Optional]">
      <option name="closed" value="true" />
      <created>1754015710828</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754015710828</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="feature_YDYCMKK-982" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="优化selenium grid连接管理&#10;&#10;Code Source From: Self Code&#10;Description:  [Optional]&#10;Jira:  #AIDT-60&#10;市场项目编号（名称）：[Optional]" />
    <MESSAGE value="优化回调函数&#10;&#10;Code Source From: Self Code&#10;Description:  [Optional]&#10;Jira:  #AIDT-60&#10;市场项目编号（名称）：[Optional]" />
    <MESSAGE value="0801" />
    <MESSAGE value="UI测试脚本支持执行&#10;&#10;Code Source From: Self Code&#10;Description:  [Optional]&#10;Jira:  #AIDT-62&#10;市场项目编号（名称）：[Optional]" />
    <option name="LAST_COMMIT_MESSAGE" value="UI测试脚本支持执行&#10;&#10;Code Source From: Self Code&#10;Description:  [Optional]&#10;Jira:  #AIDT-62&#10;市场项目编号（名称）：[Optional]" />
  </component>
</project>