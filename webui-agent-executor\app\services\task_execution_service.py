"""进程管理服务 - 使用RabbitMQ消息队列方案"""

import asyncio
import base64
import multiprocessing
import os
import time
from io import BytesIO
from pathlib import Path

from browser_use import Agent, BrowserSession
from PIL import Image

from app.config.enums import ExecutionStatus
from app.config.settings import settings
from app.models.task import Task
from app.schemas.cookie import CookieModel
from app.utils.browser_use_deepseek import BrowserUseDeepSeek
from app.utils.cloud_login_strategy import create_cloud_login_strategy
from app.utils.custom_actions import create_cookie_manager, get_custom_controller
from app.utils.history_helper import convert_from_to
from app.utils.log import logger
from app.utils.mq_helper import (
    cleanup_mq_producer,
    send_append_screenshot_path_message,
    send_selenium_session_message,
    send_step_log_message,
    send_task_status_message,
)
from app.utils.selenium_grid import disconnect_from_selenium, get_selenium_cdp_url
from app.utils.timezone import get_shanghai_now, to_shanghai_tz

# 存储任务文件路径的全局字典
task_file_paths = {}

# 本地进程管理字典
local_processes: dict[str, multiprocessing.Process] = {}


async def save_screenshot_to_storage(screenshot_data: str, ticket_id: str) -> str:
    """
    保存截图到存储（MinIO或本地）

    Args:
        screenshot_data: base64编码的截图数据
        ticket_id: 任务ID

    Returns:
        str: 保存的文件路径（MinIO对象路径或本地路径）
    """
    try:
        # 生成文件名
        timestamp = str(time.time()).replace(".", "_")
        filename = f"{timestamp}.png"

        # 解码base64数据
        image_data = base64.b64decode(screenshot_data)

        if settings.minio_enabled:
            # 使用MinIO存储
            from app.utils.minio_storage import get_minio_storage

            storage = get_minio_storage()
            object_name = storage.generate_object_name(ticket_id, "screenshot", filename)
            await storage.upload_data(image_data, object_name, "image/png")

            logger.info(f"截图已保存到MinIO: {object_name}")
            return object_name
        else:
            # 使用本地存储
            screenshot_dir = Path(settings.screenshot_dir) / ticket_id
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            local_path = screenshot_dir / filename

            # 保存到本地
            Image.open(BytesIO(image_data)).save(local_path)
            logger.info(f"截图已保存到本地: {local_path}")
            return str(local_path)

    except Exception as e:
        logger.error(f"保存截图失败: {e}")
        # 如果保存失败，尝试保存到本地作为备选
        try:
            screenshot_dir = Path(settings.screenshot_dir) / ticket_id
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            local_path = screenshot_dir / filename
            Image.open(BytesIO(base64.b64decode(screenshot_data))).save(local_path)
            logger.info(f"截图备选保存到本地: {local_path}")
            return str(local_path)
        except Exception as backup_e:
            logger.error(f"备选保存也失败: {backup_e}")
            raise


async def run_with_browser_monitoring(agent, browser_session, ticket_id, initial_actions=None):
    """
    运行Agent任务，同时监控浏览器会话状态

    Args:
        agent: Browser Use Agent实例
        browser_session: 浏览器会话对象
        ticket_id: 任务ID
        initial_actions: 初始化动作

    Returns:
        Agent运行的结果

    Raises:
        RuntimeError: 当浏览器会话为None时
    """

    async def monitor_browser_session():
        """监控浏览器会话状态"""
        check_count = 0
        while True:
            try:
                # 检查浏览器会话是否为None
                if browser_session is None:
                    logger.debug(f"任务 {ticket_id} - 检查 #{check_count}: 浏览器会话为None，强制停止任务")
                    raise RuntimeError("浏览器会话为None，强制停止任务")

                # 每10次检查输出一次正常状态日志
                if check_count % 10 == 0:
                    logger.info(f"任务 {ticket_id} - 浏览器会话监控正常 (检查 #{check_count + 1}次)")

            except RuntimeError:
                # 重新抛出已知的运行时错误
                raise
            except Exception as e:
                # 捕获其他异常，可能是访问浏览器会话属性时的异常
                logger.debug(f"任务 {ticket_id} - 检查 #{check_count}: 浏览器会话状态检查异常: {str(e)}")
                raise RuntimeError(f"浏览器会话状态检查异常: {str(e)}") from e

            await asyncio.sleep(settings.browser_session_check_interval)
            check_count += 1

    async def save_log(agent: Agent):
        """保存日志到MQ、保存截图"""
        try:
            if initial_actions and len(agent.state.history.extracted_content()) == 1:
                send_step_log_message(ticket_id, "初始化动作执行完成", append=True)

            if agent.state.last_result and len(agent.state.last_result) > 0:
                extracted_content = agent.state.last_result[0].extracted_content
                if extracted_content:
                    send_step_log_message(ticket_id, extracted_content, append=True)
                    current_page = await agent.browser_session.get_current_page()
                    # 空白页面不截图
                    if current_page and current_page.url and current_page.url != "about:blank":
                        # 保存截图
                        screenshot = await agent.browser_session.take_screenshot()
                        if screenshot:  # 确保screenshot不为None
                            try:
                                # 使用新的截图保存函数
                                screenshot_path = await save_screenshot_to_storage(screenshot, ticket_id)

                                # 实时将截图路径追加到数据库中
                                send_append_screenshot_path_message(ticket_id, screenshot_path)

                                # 同时保存到内存字典中作为备份（用于任务完成时的批量处理）
                                if ticket_id not in task_file_paths:
                                    task_file_paths[ticket_id] = {"screenshots": []}
                                if "screenshots" not in task_file_paths[ticket_id]:
                                    task_file_paths[ticket_id]["screenshots"] = []
                                task_file_paths[ticket_id]["screenshots"].append(screenshot_path)

                                logger.debug(f"截图已保存并实时更新到数据库: {screenshot_path}")
                            except Exception as screenshot_e:
                                logger.error(f"保存截图失败: {screenshot_e}")
                                # 截图保存失败不应该影响主要任务执行
            else:
                send_step_log_message(ticket_id, "当前步骤没有执行相关动作，跳过", append=True)
        except Exception as e:
            # 日志保存失败不应该影响主要任务执行，只记录错误
            logger.warning(f"保存步骤日志时发生异常，但不影响任务执行: {str(e)}")

    async def run_agent_task():
        """运行Agent任务"""
        logger.info(f"任务 {ticket_id} - selenium_remote_url: {settings.selenium_remote_url}")
        logger.info(f"任务 {ticket_id} - 运行Agent开始")

        send_step_log_message(ticket_id, "开始运行Agent", append=False)

        # 如果初始化动作不为空，则追加到日志中
        if initial_actions:
            send_step_log_message(
                ticket_id, f"初始化动作: {'、 '.join(initial_actions[0].keys())}，正在依次执行。。。", append=True
            )

        result = await agent.run(on_step_end=save_log)

        send_step_log_message(ticket_id, "Agent运行完成", append=True)
        logger.info(f"任务 {ticket_id} - 运行Agent完成")
        return result

    # 同时运行Agent任务和浏览器会话监控
    try:
        # 使用asyncio.create_task创建监控任务
        monitor_task = asyncio.create_task(monitor_browser_session())
        agent_task = asyncio.create_task(run_agent_task())

        # 等待任意一个任务完成或失败
        done, pending = await asyncio.wait([agent_task, monitor_task], return_when=asyncio.FIRST_COMPLETED)

        logger.debug(f"任务 {ticket_id} - 任意一个任务完成或失败")

        # 取消所有未完成的任务
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        # 优先检查agent_task是否完成
        if agent_task in done:
            exception = agent_task.exception()
            if exception:
                raise exception
            return agent_task.result()

        # 如果agent_task未完成，检查monitor_task
        if monitor_task in done:
            exception = monitor_task.exception()
            if exception:
                raise exception
            else:
                raise RuntimeError("监控任务意外完成")

    except Exception as e:
        # 确保所有任务都被取消
        if "monitor_task" in locals():
            monitor_task.cancel()
        if "agent_task" in locals():
            agent_task.cancel()
        raise e


def agent_process_worker(
    ticket_id: str,
    task_description: str,
    username: str | None = None,
    password: str | None = None,
    provider_type: str | None = None,
    cookies: list[dict] | None = None,
):
    """
    在独立进程中运行 agent 任务的工作函数 - 直接更新数据库状态
    """
    loop = None
    browser_session = None

    try:
        logger.debug(f"进程 {os.getpid()} 开始执行任务 {ticket_id}")

        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 初始化消息生产者
        logger.debug(f"子进程 {os.getpid()} 初始化RabbitMQ生产者")

        # 更新任务状态为已开始 - 增强版本，确保状态更新成功
        max_retries = 3
        for attempt in range(max_retries):
            try:
                send_task_status_message(ticket_id, ExecutionStatus.RUNNING, None, "进程启动，开始初始化")
                logger.info(f"任务 {ticket_id} - 成功发送RUNNING状态消息 (尝试 {attempt + 1}/{max_retries})")
                break
            except Exception as start_error:
                logger.error(
                    f"任务 {ticket_id} - 发送开始状态失败 (尝试 {attempt + 1}/{max_retries}): {str(start_error)}"
                )
                if attempt == max_retries - 1:
                    logger.critical(f"任务 {ticket_id} - 所有状态更新尝试都失败，这可能导致started_at字段缺失")
                    # 即使状态更新失败，我们也继续执行任务，但记录严重错误
                else:
                    # 短暂等待后重试
                    import time

                    time.sleep(0.5)

        # 获取Selenium Grid上的session_id
        session_id = None
        selenium_session = get_selenium_cdp_url(settings.selenium_remote_url, browser_name="chrome")
        cdp_url = selenium_session["cdp_url"]
        session_id = selenium_session["session_id"]

        # 发送Selenium会话消息
        if session_id:
            send_selenium_session_message(ticket_id, session_id)

        # 准备视频录制配置
        video_config = {}
        if settings.video_recording_enabled:
            # 为每个任务创建独立的录制目录
            task_video_dir = Path(settings.video_recording_dir) / ticket_id
            task_video_dir.mkdir(parents=True, exist_ok=True)

            video_config = {
                "record_video_dir": str(task_video_dir),
                "record_video_size": {
                    "width": settings.video_recording_width,
                    "height": settings.video_recording_height,
                },
            }

        # 创建浏览器会话
        browser_session = BrowserSession(  # type: ignore
            cdp_url=cdp_url,
            headless=settings.browser_headless,
            slow_mo=settings.slow_mo,
            disable_security=True,
            chromium_sandbox=False,
            window_size={
                "width": settings.browser_window_width,
                "height": settings.browser_window_height,
            },
            args=[
                "--incognito",
            ],
            **video_config,  # 添加视频录制配置
        )

        # 创建 LLM 实例
        llm_process = BrowserUseDeepSeek(
            model=settings.openai_model,
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            temperature=settings.openai_temperature,
            max_tokens=settings.openai_max_tokens,
        )

        # 创建 Agent
        agent_kwargs = {
            "task": task_description,
            "llm": llm_process,
            "browser_session": browser_session,
            "use_vision": settings.use_vision,
            "controller": get_custom_controller(),
            "file_system_path": str(Path(settings.file_system_dir) / ticket_id),
        }

        # Cookie 批量设置功能 - 在 Agent 执行前设置 cookies
        if cookies is not None and len(cookies) > 0:
            try:
                logger.info(f"任务 {ticket_id} - 开始设置 {len(cookies)} 个 cookies")

                # 创建 CookieManager 实例
                cookie_manager = create_cookie_manager(browser_session)

                # 将字典转换为 CookieModel 对象
                cookie_models = []
                for cookie_dict in cookies:
                    try:
                        cookie_model = CookieModel(**cookie_dict)
                        cookie_models.append(cookie_model)
                    except Exception as cookie_parse_error:
                        logger.warning(
                            f"任务 {ticket_id} - 解析 cookie 失败: {cookie_dict}, 错误: {cookie_parse_error}"
                        )
                        continue

                if cookie_models:
                    # 批量设置 cookies
                    result = loop.run_until_complete(cookie_manager.set_cookies_batch(cookie_models))

                    if result.get("success", False):
                        success_count = result.get("success_count", 0)
                        logger.info(f"任务 {ticket_id} - 成功设置 {success_count} 个 cookies")
                    else:
                        logger.warning(f"任务 {ticket_id} - Cookie 设置失败: {result.get('error', '未知错误')}")
                else:
                    logger.warning(f"任务 {ticket_id} - 没有有效的 cookie 数据可设置")

            except Exception as cookie_error:
                logger.error(f"任务 {ticket_id} - Cookie 设置过程中发生异常: {cookie_error}")
                # Cookie 设置失败不应该导致整个任务失败，继续执行

        # 使用云服务登录策略工厂创建初始化动作（暂时注释）
        initial_actions = None
        if username is not None and password is not None:
            # 创建云服务登录策略
            # 如果未指定provider_type，默认使用'ecloud'（移动云），保持向后兼容
            effective_provider_type = provider_type or "ecloud"
            login_strategy = create_cloud_login_strategy(provider_type=effective_provider_type)

            initial_actions = login_strategy.get_initial_actions(username, password)

            if initial_actions:
                agent_kwargs["initial_actions"] = initial_actions
                logger.info(f"任务 {ticket_id} - 已配置{login_strategy.get_provider_name()}登录凭据，将自动执行登录")
            else:
                logger.warning(f"任务 {ticket_id} - 登录策略未生成有效的初始化动作")

        # 如果启用Planner，添加planner相关参数
        if settings.use_planner:
            # 创建 Planner LLM 实例
            planner_llm = BrowserUseDeepSeek(
                model=settings.planner_llm_model,
                api_key=settings.planner_llm_api_key,
                base_url=settings.planner_llm_base_url,
                temperature=settings.planner_llm_temperature,
                max_tokens=settings.planner_llm_max_tokens,
            )
            agent_kwargs["planner_llm"] = planner_llm
            agent_kwargs["use_vision_for_planner"] = settings.use_vision_for_planner
            agent_kwargs["planner_interval"] = settings.planner_interval
            if settings.extend_planner_system_message:
                agent_kwargs["extend_planner_system_message"] = settings.extend_planner_system_message

        # 如果扩展系统提示词不为空，添加extend_system_message参数
        if settings.extend_system_message:
            agent_kwargs["extend_system_message"] = settings.extend_system_message

        # 如果消息上下文不为空，添加message_context参数
        if settings.message_context:
            agent_kwargs["message_context"] = settings.message_context

        agent = Agent(**agent_kwargs)

        # 运行任务，同时监控浏览器会话状态
        result = loop.run_until_complete(
            run_with_browser_monitoring(agent, browser_session, ticket_id, initial_actions)
        )

        logger.info(f"Agent运行完成: {ticket_id}")

        # 处理结果并直接更新数据库
        try:
            if result and hasattr(result, "model_dump"):
                history = result.model_dump()["history"]
                history_simplify = convert_from_to(history)

                # 检查history_simplify是否为空或null
                if not history_simplify:
                    send_task_status_message(
                        ticket_id, ExecutionStatus.FAILED, "未执行任何操作", "任务完成但未产生有效结果"
                    )
                else:
                    send_task_status_message(ticket_id, ExecutionStatus.SUCCESS, history_simplify, "任务执行成功")

                    # 上传任务文件到存储
                    try:
                        loop.run_until_complete(upload_task_files_to_storage(ticket_id))
                    except Exception as upload_error:
                        logger.error(f"上传任务文件失败: {ticket_id}, 错误: {upload_error}")
                        # 文件上传失败不影响任务成功状态
            else:
                send_task_status_message(
                    ticket_id, ExecutionStatus.FAILED, "Agent 执行结果为空或格式异常", "任务执行异常"
                )
        except Exception as status_update_error:
            logger.critical(f"任务 {ticket_id} - 更新最终状态失败: {str(status_update_error)}")
            # 这是关键异常，至少要尝试设置一个基本的失败状态
            try:
                now = get_shanghai_now()
                loop.run_until_complete(
                    Task.filter(ticket_id=ticket_id).update(
                        status=ExecutionStatus.FAILED,
                        error_message=f"状态更新失败: {str(status_update_error)}",
                        completed_at=now,
                        updated_at=now,
                    )
                )
            except Exception as final_error:
                logger.critical(f"任务 {ticket_id} - 最终状态更新也失败: {str(final_error)}")

    except KeyboardInterrupt:
        logger.info(f"任务被取消: {ticket_id}")
        try:
            send_task_status_message(ticket_id, ExecutionStatus.CANCELLED, "任务被用户取消")
        except Exception as cancel_error:
            logger.error(f"任务 {ticket_id} - 发送取消状态失败: {str(cancel_error)}")

    except Exception as e:
        error_message = f"Agent 执行异常: {str(e)}"

        # 检查是否是浏览器会话相关的异常
        if "浏览器会话" in str(e) or "浏览器页面" in str(e) or "浏览器上下文" in str(e):
            error_message = f"浏览器会话异常，任务被强制停止: {str(e)}"

        logger.error(f"任务执行异常: {ticket_id}, {error_message}")

        try:
            send_task_status_message(ticket_id, ExecutionStatus.FAILED, error_message)
        except Exception as error_update_error:
            logger.error(f"任务 {ticket_id} - 发送异常状态失败: {str(error_update_error)}")
    finally:
        # 清理资源
        if browser_session and loop and not loop.is_closed():
            try:
                loop.run_until_complete(browser_session.close())
            except Exception as e:
                logger.debug(f"关闭浏览器会话时出错: {str(e)}")

        # 清理RabbitMQ生产者
        try:
            cleanup_mq_producer()
            logger.debug(f"子进程 {os.getpid()} RabbitMQ生产者清理完成")
        except Exception as e:
            logger.debug(f"清理RabbitMQ生产者时出错: {str(e)}")

        if loop:
            try:
                loop.close()
            except Exception as e:
                logger.debug(f"关闭事件循环时出错: {str(e)}")


async def start_agent_process(
    ticket_id: str,
    task_description: str,
    username: str | None = None,
    password: str | None = None,
    provider_type: str | None = None,
    cookies: list[dict] | None = None,
):
    """启动进程来运行 agent 任务"""
    process = multiprocessing.Process(
        target=agent_process_worker,
        args=(ticket_id, task_description, username, password, provider_type, cookies),
        daemon=True,
    )

    local_processes[ticket_id] = process

    # 启动进程
    logger.info(f"开始执行任务: {ticket_id}")
    process.start()

    # 启动简化的监控任务（只负责超时和取消检查）
    asyncio.create_task(monitor_process(ticket_id, process))


async def terminate_process(process):
    """终止指定的进程"""
    try:
        process.terminate()

        # 等待进程响应 SIGTERM
        timeout_steps = int(settings.process_terminate_timeout / 0.5)
        for _ in range(timeout_steps):
            if not process.is_alive():
                return
            await asyncio.sleep(0.5)

        # 如果仍在运行，发送 SIGKILL
        if process.is_alive():
            process.kill()

            kill_timeout_steps = int(settings.process_kill_timeout / 0.5)
            for _ in range(kill_timeout_steps):
                if not process.is_alive():
                    return
                await asyncio.sleep(0.5)

    except Exception as e:
        logger.debug(f"终止进程时发生异常: {str(e)}")


async def monitor_process(ticket_id: str, process):
    """
    进程监控 - 只负责超时和取消的检查
    """
    try:
        while process.is_alive():
            logger.debug(f"监控任务 {ticket_id} - 进程仍在运行")
            task = await Task.get_or_none(ticket_id=ticket_id)
            if not task:
                logger.warning(f"监控任务 {ticket_id} - 数据库中未找到任务记录，停止监控")
                break

            # 检查是否被取消
            if task.status == ExecutionStatus.CANCELLED:
                logger.info(f"监控任务 {ticket_id} - 任务已被取消，正在终止进程")
                await terminate_process(process)
                break

            # 检查是否超时
            if task.started_at:
                now = get_shanghai_now()
                task_started = to_shanghai_tz(task.started_at)
                elapsed = now - task_started
                if elapsed.total_seconds() > settings.task_timeout_minutes * 60:
                    logger.warning(f"监控任务 {ticket_id} - 任务执行超时，已运行 {elapsed.total_seconds():.1f} 秒")

                    # 标记任务为超时失败
                    await Task.filter(ticket_id=ticket_id).update(
                        status=ExecutionStatus.FAILED,
                        error_message=f"任务执行超时（超过{settings.task_timeout_minutes}分钟）",
                        completed_at=now,
                        updated_at=now,
                    )

                    await terminate_process(process)
                    break

            await asyncio.sleep(settings.monitor_check_interval)

        logger.info(f"监控任务 {ticket_id} - 进程已结束，监控完成")

    except Exception as e:
        logger.error(f"监控任务时发生异常: {ticket_id}, {str(e)}")
        # 在监控异常时，尝试将任务标记为失败
        try:
            now = get_shanghai_now()
            await Task.filter(ticket_id=ticket_id).update(
                status=ExecutionStatus.FAILED,
                error_message=f"监控进程时出错: {str(e)}",
                completed_at=now,
                updated_at=now,
            )
        except Exception as update_error:
            logger.error(f"更新任务状态失败: {ticket_id}, {str(update_error)}")
    finally:
        # 关闭Selenium Grid上的session
        task = await Task.get_or_none(ticket_id=ticket_id)
        if task and task.selenium_session_id:
            disconnect_from_selenium(settings.selenium_remote_url, task.selenium_session_id)
        # 清理本地进程记录
        if ticket_id in local_processes:
            del local_processes[ticket_id]
            logger.debug(f"清理本地进程记录: {ticket_id}")


async def terminate_task_process(ticket_id: str) -> tuple[bool, str]:
    """终止指定任务的进程"""
    task = await Task.get_or_none(ticket_id=ticket_id)
    if not task:
        logger.warning(f"任务不存在: {ticket_id}")
        return False, "任务不存在"

    try:
        # 关闭Selenium Grid上的session
        if task.selenium_session_id:
            disconnect_from_selenium(settings.selenium_remote_url, task.selenium_session_id)

        # 标记任务为取消状态 - 优化版本，保护数据完整性
        now = get_shanghai_now()

        # 等待一小段时间，让正在处理的消息完成
        logger.info(f"任务 {ticket_id} - 等待消息队列处理完成...")
        await asyncio.sleep(2.0)  # 给消息处理留出时间

        # 获取当前任务状态，保留已设置的关键字段
        current_task = await Task.get_or_none(ticket_id=ticket_id)
        update_data = {
            "status": ExecutionStatus.CANCELLED,
            "error_message": "任务被手动取消",
            "updated_at": now,
        }

        # 只有在completed_at未设置时才设置它
        if not current_task.completed_at:
            update_data["completed_at"] = now
            logger.info(f"任务 {ticket_id} - 设置completed_at为取消时间")
        else:
            logger.info(f"任务 {ticket_id} - 保留现有的completed_at时间")

        await Task.filter(ticket_id=ticket_id).update(**update_data)
        logger.info(f"任务 {ticket_id} - 已标记为CANCELLED状态，数据完整性已保护")

        if task.replica_id == settings.replica_id and ticket_id in local_processes:
            process = local_processes[ticket_id]

            if process.is_alive():
                process.terminate()
                process.join(timeout=settings.process_terminate_timeout)

                if process.is_alive():
                    process.kill()
                    process.join(timeout=settings.process_kill_timeout)

            del local_processes[ticket_id]
            logger.info(f"任务已从本地进程列表中移除: {ticket_id}")
            return True, "任务已成功终止（本地进程）"

        elif task.replica_id and task.replica_id != settings.replica_id:
            logger.info(f"任务在远程副本 {task.replica_id} 运行，仅标记为取消: {ticket_id}")
            return True, f"任务在副本 {task.replica_id} 运行，已标记为取消"
        else:
            logger.info(f"任务无副本信息，仅标记为取消: {ticket_id}")
            return True, "任务已标记为取消"

    except Exception as e:
        logger.error(f"终止任务进程时发生异常: {ticket_id}, 错误: {str(e)}")
        return False, f"任务终止过程中出现异常: {str(e)}"


async def upload_task_files_to_storage(ticket_id: str):
    """
    将任务相关文件上传到存储并更新数据库

    Args:
        ticket_id: 任务ID
    """
    try:
        if not settings.minio_enabled:
            logger.info(f"MinIO存储未启用，跳过文件上传: {ticket_id}")
            return

        from app.utils.minio_storage import get_minio_storage

        storage = get_minio_storage()

        # 处理视频文件 - 改为实时更新模式
        if settings.video_recording_enabled:
            video_dir = Path(settings.video_recording_dir) / ticket_id
            if video_dir.exists():
                # 查找所有视频文件（只支持.webm格式）
                video_files = list(video_dir.glob("*.webm"))
                if video_files:
                    for video_file in video_files:
                        try:
                            object_name = storage.generate_object_name(ticket_id, "video", video_file.name)
                            await storage.upload_file(video_file, object_name)
                            logger.info(f"视频文件已上传: {object_name}")

                            # 实时将视频路径追加到数据库中
                            from app.utils.mq_helper import send_append_video_path_message

                            send_append_video_path_message(ticket_id, object_name)

                        except Exception as e:
                            logger.error(f"上传视频文件失败: {video_file}, 错误: {e}")

        # 处理下载文件
        download_dir = Path(settings.file_system_dir) / ticket_id
        if download_dir.exists():
            # 查找所有下载的文件
            download_files = []
            for file_path in download_dir.rglob("*"):
                if file_path.is_file():
                    download_files.append(file_path)

            if download_files:
                for download_file in download_files:
                    try:
                        # 保持相对路径结构
                        relative_path = download_file.relative_to(download_dir)
                        object_name = storage.generate_object_name(ticket_id, "file", str(relative_path))
                        await storage.upload_file(download_file, object_name)
                        logger.info(f"下载文件已上传: {object_name}")

                        # 实时将下载文件路径追加到数据库中
                        from app.utils.mq_helper import send_append_file_path_message

                        send_append_file_path_message(ticket_id, object_name)

                    except Exception as e:
                        logger.error(f"上传下载文件失败: {download_file}, 错误: {e}")

        # 所有文件类型现在都采用实时更新模式，不再需要批量更新
        logger.info(f"所有文件已采用实时更新模式处理完成: {ticket_id}")

        # 清理全局字典中的任务数据
        if ticket_id in task_file_paths:
            del task_file_paths[ticket_id]

        logger.info(f"任务文件上传和数据库更新完成: {ticket_id}")

    except Exception as e:
        logger.error(f"上传任务文件失败: {ticket_id}, 错误: {e}")
