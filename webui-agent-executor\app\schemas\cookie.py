"""
Cookie 相关的 Pydantic 模型

定义 Cookie 操作的数据结构和验证规则
"""

from pydantic import BaseModel, Field, field_validator


class CookieModel(BaseModel):
    """Cookie 数据模型"""

    name: str = Field(..., description="Cookie 名称")
    value: str = Field(..., description="Cookie 值")
    domain: str = Field(..., description="Cookie 域名")
    path: str = Field(default="/", description="Cookie 路径")
    expires: float | None = Field(default=None, description="过期时间（Unix时间戳）")
    httpOnly: bool | None = Field(default=False, description="是否仅HTTP访问")
    secure: bool | None = Field(default=False, description="是否仅HTTPS")
    sameSite: str | None = Field(default="Lax", description="SameSite 策略")

    @field_validator("name")
    @classmethod
    def validate_name(cls, value: str) -> str:
        """验证 Cookie 名称不能为空"""
        if not value or not value.strip():
            raise ValueError("Cookie 名称不能为空")
        return value.strip()

    @field_validator("value")
    @classmethod
    def validate_value(cls, value: str) -> str:
        """验证 Cookie 值不能为空"""
        if not value or not value.strip():
            raise ValueError("Cookie 值不能为空")
        return value.strip()

    @field_validator("domain")
    @classmethod
    def validate_domain(cls, value: str) -> str:
        """验证 Cookie 域名格式"""
        if not value or not value.strip():
            raise ValueError("Cookie 域名不能为空")

        domain = value.strip()

        # 基本的域名格式验证
        if not domain.replace(".", "").replace("-", "").replace("_", "").isalnum():
            # 允许字母、数字、点、连字符和下划线
            if not all(c.isalnum() or c in ".-_" for c in domain):
                raise ValueError("Cookie 域名包含无效字符")

        return domain

    @field_validator("path")
    @classmethod
    def validate_path(cls, value: str) -> str:
        """验证 Cookie 路径格式"""
        if not value:
            return "/"

        path = value.strip()
        if not path.startswith("/"):
            path = "/" + path

        return path

    @field_validator("sameSite")
    @classmethod
    def validate_same_site(cls, value: str | None) -> str | None:
        """验证 SameSite 策略"""
        if value is None:
            return value

        valid_values = ["Strict", "Lax", "None"]
        if value not in valid_values:
            raise ValueError(f"无效的 SameSite 值: {value}，支持的值: {', '.join(valid_values)}")

        return value


class SetCookiesBatchAction(BaseModel):
    """批量设置 Cookie 的动作参数"""

    cookies: list[CookieModel] = Field(..., description="要设置的 Cookie 列表")

    @field_validator("cookies")
    @classmethod
    def validate_cookies(cls, value: list[CookieModel]) -> list[CookieModel]:
        """验证 Cookie 列表不能为空"""
        if not value:
            raise ValueError("Cookie 列表不能为空")

        if len(value) > 100:  # 限制批量操作的数量
            raise ValueError("单次批量操作最多支持 100 个 cookies")

        # 检查是否有重复的 cookie（相同的 name + domain 组合）
        seen_cookies = set()
        for cookie in value:
            cookie_key = f"{cookie.domain}:{cookie.name}"
            if cookie_key in seen_cookies:
                raise ValueError(f"发现重复的 cookie: {cookie.name} @ {cookie.domain}")
            seen_cookies.add(cookie_key)

        return value


class CookieOperationResult(BaseModel):
    """Cookie 操作结果模型"""

    total: int = Field(..., description="总数量")
    success: int = Field(..., description="成功数量")
    failed: int = Field(..., description="失败数量")
    failed_cookies: list[str] = Field(default_factory=list, description="失败的 cookie 列表")
    message: str = Field(..., description="操作结果描述")


class CookieQueryRequest(BaseModel):
    """查询 Cookie 的请求模型"""

    domain: str | None = Field(None, description="可选的域名过滤")
    name: str | None = Field(None, description="可选的 Cookie 名称过滤")

    @field_validator("domain")
    @classmethod
    def validate_domain(cls, value: str | None) -> str | None:
        """验证域名格式"""
        if value is None:
            return value

        domain = value.strip()
        if not domain:
            return None

        return domain

    @field_validator("name")
    @classmethod
    def validate_name(cls, value: str | None) -> str | None:
        """验证 Cookie 名称"""
        if value is None:
            return value

        name = value.strip()
        if not name:
            return None

        return name


class CookieDeleteRequest(BaseModel):
    """删除 Cookie 的请求模型"""

    name: str = Field(..., description="要删除的 Cookie 名称")
    domain: str = Field(..., description="要删除的 Cookie 域名")

    @field_validator("name")
    @classmethod
    def validate_name(cls, value: str) -> str:
        """验证 Cookie 名称不能为空"""
        if not value or not value.strip():
            raise ValueError("Cookie 名称不能为空")
        return value.strip()

    @field_validator("domain")
    @classmethod
    def validate_domain(cls, value: str) -> str:
        """验证 Cookie 域名不能为空"""
        if not value or not value.strip():
            raise ValueError("Cookie 域名不能为空")
        return value.strip()
