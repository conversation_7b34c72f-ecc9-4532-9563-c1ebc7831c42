"""
任务数据库模型

定义任务在数据库中的结构和字段
"""

from tortoise import fields
from tortoise.models import Model

from app.config.enums import ExecutionStatus


class Task(Model):
    """
    任务数据库模型

    这个模型存储所有副本的任务信息，实现跨副本的任务状态共享：
    - 任务创建时记录在哪个副本上创建
    - 任务执行时记录在哪个副本上运行
    - 任务终止时可以跨副本进行控制
    """

    # 自增主键ID
    id = fields.IntField(pk=True, description="主键ID")

    # 任务唯一标识符（原来的id字段，保持业务逻辑不变）
    ticket_id = fields.CharField(max_length=50, unique=True, description="任务唯一标识符")

    # 任务状态：PENDING(等待) -> RUNNING(运行中) -> SUCCESS(成功)/FAILURE(失败)/CANCELLED(取消)
    status = fields.CharEnumField(ExecutionStatus, default=ExecutionStatus.PENDING, description="任务状态")

    # 用户提交的任务描述
    task_description = fields.TextField(description="用户提交的任务描述")

    # 任务执行步骤日志
    step_log = fields.TextField(null=True, description="任务执行步骤日志")

    # 任务执行成功时的结果（JSON 格式存储历史记录）
    result = fields.JSONField(null=True, description="任务执行结果")

    # 任务执行失败时的错误信息
    error_message = fields.TextField(null=True, description="任务执行失败时的错误信息")

    # 处理该任务的副本ID（哪个 Pod 在执行这个任务）
    replica_id = fields.CharField(max_length=100, null=True, description="处理该任务的副本ID")

    # Selenium会话ID
    selenium_session_id = fields.CharField(max_length=50, null=True, description="Selenium会话ID")

    # 任务创建时间
    created_at = fields.DatetimeField(auto_now_add=True, description="任务创建时间")

    # 任务最后更新时间
    updated_at = fields.DatetimeField(auto_now=True, description="任务最后更新时间")

    # 任务开始执行时间（用于超时检查）
    started_at = fields.DatetimeField(null=True, description="任务开始执行时间")

    # 任务执行完成时间
    completed_at = fields.DatetimeField(null=True, description="任务执行完成时间")

    # ==================== 文件存储相关字段 ====================

    # 视频录制文件路径列表（JSON格式存储多个视频文件路径）
    video_file_paths = fields.JSONField(null=True, description="视频录制文件路径列表")

    # 截图文件路径列表（JSON格式存储多个截图路径）
    screenshot_file_paths = fields.JSONField(null=True, description="截图文件路径列表")

    # 下载文件路径列表（JSON格式存储多个下载文件路径）
    download_file_paths = fields.JSONField(null=True, description="下载文件路径列表")

    class Meta:
        table = "task"
        table_description = "任务数据库模型"
