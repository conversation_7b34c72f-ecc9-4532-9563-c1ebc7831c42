"""
RabbitMQ消息生产者服务

负责发送数据库操作消息到RabbitMQ
"""

import uuid
from typing import Any

import pika
from pika.adapters.blocking_connection import BlockingChannel
from pika.exceptions import AMQPChannelError, AMQPConnectionError

from app.config.enums import ExecutionStatus
from app.config.settings import settings
from app.schemas.mq_message import (
    AppendLogMessage,
    AppendScreenshotPathMessage,
    DatabaseMessage,
    DatabaseOperationType,
    MessageResponse,
    UpdateErrorMessage,
    UpdateFilePathsMessage,
    UpdateResultMessage,
    UpdateSeleniumSessionMessage,
    UpdateStatusMessage,
)
from app.utils.log import logger


class DatabaseMessageProducer:
    """数据库操作消息生产者"""

    def __init__(self):
        self.connection: pika.BlockingConnection | None = None
        self.channel: BlockingChannel | None = None
        self._initialized = False

    def init_producer(self) -> bool:
        """初始化生产者"""
        try:
            if self._initialized and self.connection and not self.connection.is_closed:
                return True

            # 创建连接参数
            credentials = pika.PlainCredentials(settings.rabbitmq_username, settings.rabbitmq_password)
            parameters = pika.ConnectionParameters(
                host=settings.rabbitmq_host,
                port=settings.rabbitmq_port,
                virtual_host=settings.rabbitmq_vhost,
                credentials=credentials,
                heartbeat=settings.rabbitmq_heartbeat,
                connection_attempts=3,
                retry_delay=2,
            )

            # 建立连接
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()

            # 声明交换机和队列
            assert self.channel is not None  # 类型检查断言
            self.channel.exchange_declare(
                exchange=settings.rabbitmq_db_exchange, exchange_type="direct", durable=settings.rabbitmq_durable
            )

            self.channel.queue_declare(queue=settings.rabbitmq_db_queue, durable=settings.rabbitmq_durable)

            # 绑定队列到交换机
            self.channel.queue_bind(
                exchange=settings.rabbitmq_db_exchange,
                queue=settings.rabbitmq_db_queue,
                routing_key=settings.rabbitmq_routing_key,
            )

            self._initialized = True
            logger.info(f"RabbitMQ生产者初始化成功: {settings.rabbitmq_host}:{settings.rabbitmq_port}")
            return True

        except AMQPConnectionError as e:
            logger.error(f"RabbitMQ连接失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"RabbitMQ生产者初始化失败: {str(e)}")
            return False

    def shutdown(self):
        """关闭生产者"""
        try:
            if self.channel and not self.channel.is_closed:
                self.channel.close()
                self.channel = None

            if self.connection and not self.connection.is_closed:
                self.connection.close()
                self.connection = None

            self._initialized = False
            logger.info("RabbitMQ生产者已关闭")
        except Exception as e:
            logger.error(f"关闭RabbitMQ生产者失败: {str(e)}")

    def _send_message(self, message: DatabaseMessage) -> MessageResponse:
        """发送消息的通用方法"""
        if not self.init_producer():
            return MessageResponse(success=False, error="生产者初始化失败")

        try:
            # 序列化消息
            message_body = message.model_dump_json()

            # 发送消息
            assert self.channel is not None  # 类型检查断言
            self.channel.basic_publish(
                exchange=settings.rabbitmq_db_exchange,
                routing_key=settings.rabbitmq_routing_key,
                body=message_body.encode("utf-8"),
                properties=pika.BasicProperties(
                    delivery_mode=2 if settings.rabbitmq_durable else 1,  # 消息持久化
                    headers={
                        "operation_type": message.operation_type.value,
                        "ticket_id": message.ticket_id,
                        "message_id": message.message_id,
                        "retry_count": message.retry_count,
                    },
                ),
            )

            logger.debug(f"消息发送成功: {message.message_id}, 操作: {message.operation_type.value}")

            return MessageResponse(success=True, message_id=message.message_id)

        except AMQPChannelError as e:
            logger.error(f"RabbitMQ通道错误: {message.message_id}, 错误: {str(e)}")
            self._initialized = False  # 重置状态，下次调用时重新初始化
            return MessageResponse(success=False, error=str(e))
        except Exception as e:
            logger.error(f"发送消息失败: {message.message_id}, 错误: {str(e)}")
            return MessageResponse(success=False, error=str(e))

    def send_update_status(
        self,
        ticket_id: str,
        status: ExecutionStatus,
        result_or_error: Any | None = None,
        step_log_message: str | None = None,
    ) -> MessageResponse:
        """发送更新任务状态消息"""
        message_id = str(uuid.uuid4())

        data = UpdateStatusMessage(
            status=status, result_or_error=result_or_error, step_log_message=step_log_message
        ).model_dump()

        message = DatabaseMessage(
            message_id=message_id,
            operation_type=DatabaseOperationType.UPDATE_STATUS,
            ticket_id=ticket_id,
            data=data,
            max_retry=settings.rabbitmq_max_retry_times,
        )

        return self._send_message(message)

    def send_selenium_session(self, ticket_id: str, session_id: str) -> MessageResponse:
        """发送Selenium会话消息"""
        message_id = str(uuid.uuid4())

        data = UpdateSeleniumSessionMessage(session_id=session_id).model_dump()

        message = DatabaseMessage(
            message_id=message_id,
            operation_type=DatabaseOperationType.UPDATE_SELENIUM_SESSION,
            ticket_id=ticket_id,
            data=data,
            max_retry=settings.rabbitmq_max_retry_times,
        )

        return self._send_message(message)

    def send_append_log(self, ticket_id: str, message_text: str, append: bool = True) -> MessageResponse:
        """发送追加日志消息"""
        message_id = str(uuid.uuid4())

        data = AppendLogMessage(message=message_text, append=append).model_dump()

        message = DatabaseMessage(
            message_id=message_id,
            operation_type=DatabaseOperationType.APPEND_LOG,
            ticket_id=ticket_id,
            data=data,
            max_retry=settings.rabbitmq_max_retry_times,
        )

        return self._send_message(message)

    def send_update_result(self, ticket_id: str, result: Any) -> MessageResponse:
        """发送更新结果消息"""
        message_id = str(uuid.uuid4())

        data = UpdateResultMessage(result=result).model_dump()

        message = DatabaseMessage(
            message_id=message_id,
            operation_type=DatabaseOperationType.UPDATE_RESULT,
            ticket_id=ticket_id,
            data=data,
            max_retry=settings.rabbitmq_max_retry_times,
        )

        return self._send_message(message)

    def send_update_error(self, ticket_id: str, error_message: str) -> MessageResponse:
        """发送更新错误消息"""
        message_id = str(uuid.uuid4())

        data = UpdateErrorMessage(error_message=error_message).model_dump()

        message = DatabaseMessage(
            message_id=message_id,
            operation_type=DatabaseOperationType.UPDATE_ERROR,
            ticket_id=ticket_id,
            data=data,
            max_retry=settings.rabbitmq_max_retry_times,
        )

        return self._send_message(message)

    def send_update_file_paths(self, ticket_id: str, file_paths_data: dict) -> MessageResponse:
        """发送更新文件路径消息"""
        message_id = str(uuid.uuid4())

        data = UpdateFilePathsMessage(**file_paths_data).model_dump()

        message = DatabaseMessage(
            message_id=message_id,
            operation_type=DatabaseOperationType.UPDATE_FILE_PATHS,
            ticket_id=ticket_id,
            data=data,
            max_retry=settings.rabbitmq_max_retry_times,
        )

        return self._send_message(message)

    def send_append_screenshot_path(self, ticket_id: str, screenshot_path: str) -> MessageResponse:
        """发送追加截图路径消息"""
        message_id = str(uuid.uuid4())

        data = AppendScreenshotPathMessage(screenshot_path=screenshot_path).model_dump()

        message = DatabaseMessage(
            message_id=message_id,
            operation_type=DatabaseOperationType.APPEND_SCREENSHOT_PATH,
            ticket_id=ticket_id,
            data=data,
            max_retry=settings.rabbitmq_max_retry_times,
        )

        return self._send_message(message)

    def send_append_video_path(self, ticket_id: str, video_path: str) -> MessageResponse:
        """发送追加视频路径消息"""
        message_id = str(uuid.uuid4())

        from app.schemas.mq_message import AppendVideoPathMessage

        data = AppendVideoPathMessage(video_path=video_path).model_dump()

        message = DatabaseMessage(
            message_id=message_id,
            operation_type=DatabaseOperationType.APPEND_VIDEO_PATH,
            ticket_id=ticket_id,
            data=data,
            max_retry=settings.rabbitmq_max_retry_times,
        )

        return self._send_message(message)

    def send_append_file_path(self, ticket_id: str, file_path: str) -> MessageResponse:
        """发送追加下载文件路径消息"""
        message_id = str(uuid.uuid4())

        from app.schemas.mq_message import AppendFilePathMessage

        data = AppendFilePathMessage(file_path=file_path).model_dump()

        message = DatabaseMessage(
            message_id=message_id,
            operation_type=DatabaseOperationType.APPEND_FILE_PATH,
            ticket_id=ticket_id,
            data=data,
            max_retry=settings.rabbitmq_max_retry_times,
        )

        return self._send_message(message)


# 全局生产者实例（用于子进程）
_global_producer: DatabaseMessageProducer | None = None


def get_producer() -> DatabaseMessageProducer:
    """获取全局生产者实例"""
    global _global_producer
    if _global_producer is None:
        _global_producer = DatabaseMessageProducer()
    return _global_producer


def cleanup_producer():
    """清理全局生产者实例"""
    global _global_producer
    if _global_producer:
        _global_producer.shutdown()
        _global_producer = None
