"""
脚本业务逻辑服务

负责脚本的创建、查询、管理等业务逻辑
"""

import uuid

from app.config.settings import settings
from app.models.script import Script
from app.schemas.script import (
    ScriptResultResponse,
    ScriptStatusResponse,
)
from app.utils.log import logger
from app.utils.timezone import get_shanghai_now


async def create_script(script_content: str) -> str:
    """
    创建新脚本记录

    Args:
        script_content: 脚本内容

    Returns:
        str: 脚本ID
    """
    ticket_id = str(uuid.uuid4().hex)
    now = get_shanghai_now()

    await Script.create(
        ticket_id=ticket_id,
        script_content=script_content,
        status="PENDING",
        replica_id=settings.replica_id,
        created_at=now,
        updated_at=now,
    )

    logger.info(f"创建脚本记录: ticket_id={ticket_id}")
    return ticket_id


async def get_script_result(ticket_id: str) -> ScriptResultResponse:
    """
    获取脚本执行结果

    Args:
        ticket_id: 脚本ID

    Returns:
        ScriptResultResponse: 脚本执行结果
    """
    script = await Script.get(ticket_id=ticket_id)

    return ScriptResultResponse(
        ticket_id=script.ticket_id,
        status=script.status,
        script_content=script.script_content,
        stdout=script.stdout,
        stderr=script.stderr,
        return_code=script.return_code,
        replica_id=script.replica_id,
        selenium_session_id=script.selenium_session_id,
        created_at=script.created_at,
        updated_at=script.updated_at,
        started_at=script.started_at,
        completed_at=script.completed_at,
    )


async def get_script_status(ticket_id: str) -> ScriptStatusResponse:
    """
    获取脚本状态（轻量级）

    Args:
        ticket_id: 脚本ID

    Returns:
        ScriptStatusResponse: 脚本状态
    """
    script = await Script.get(ticket_id=ticket_id)

    return ScriptStatusResponse(
        ticket_id=script.ticket_id,
        status=script.status,
        replica_id=script.replica_id,
    )
