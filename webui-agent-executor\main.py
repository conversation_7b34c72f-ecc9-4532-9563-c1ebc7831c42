"""
应用启动入口

这是重构后的启动脚本，指向新的应用结构
"""

from app.main import app

if __name__ == "__main__":
    import uvicorn

    from app.config.settings import settings
    from app.utils.log import logger

    logger.info(f"启动服务器 - Host: {settings.host}, Port: {settings.port}")
    logger.info(f"调试模式: {settings.debug}")
    logger.info(f"副本ID: {settings.replica_id}")

    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        access_log=True,  # 启用访问日志
        log_config=None,  # 禁用 uvicorn 的日志配置，使用我们的拦截器
    )
