Index: app/api/routes/task.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\n任务相关的 API 路由\r\n\r\n包含所有任务管理的 HTTP 接口\r\n\"\"\"\r\n\r\nfrom fastapi import APIRouter\r\n\r\nfrom app.schemas.task import (\r\n    QueryRequest,\r\n    QueryResponse,\r\n    TaskFilesResponse,\r\n    TaskResultResponse,\r\n    TaskStatusResponse,\r\n    TerminateResponse,\r\n)\r\nfrom app.services.process_service import start_agent_process, terminate_task_process\r\nfrom app.services.task_service import (\r\n    create_task,\r\n    get_task_files,\r\n    get_task_result,\r\n    get_task_status,\r\n)\r\n\r\nrouter = APIRouter()\r\n\r\n\r\<EMAIL>(\"/query\", response_model=QueryResponse)\r\nasync def query(request: QueryRequest):\r\n    \"\"\"\r\n    创建新的浏览器自动化任务\r\n\r\n    流程：\r\n    1. 生成唯一的任务ID\r\n    2. 在数据库中创建任务记录\r\n    3. 启动进程执行任务\r\n    4. 立即返回任务ID\r\n    \"\"\"\r\n    # 创建任务记录\r\n    ticket_id = await create_task(request.task)\r\n\r\n    # 启动任务进程，传递可选的登录凭据和云服务提供商类型\r\n    await start_agent_process(ticket_id, request.task, request.username, request.password, request.provider_type)\r\n\r\n    return QueryResponse(ticket_id=ticket_id, status=\"PENDING\", message=\"任务已创建并开始执行\")\r\n\r\n\r\<EMAIL>(\"/result/{ticket_id}\", response_model=TaskResultResponse)\r\nasync def get_result(ticket_id: str):\r\n    \"\"\"\r\n    获取任务的详细结果\r\n\r\n    返回完整的任务信息，包括：\r\n    - 任务状态\r\n    - 任务执行步骤日志\r\n    - 执行结果或错误信息\r\n    - 执行副本信息\r\n    - 时间戳\r\n    \"\"\"\r\n    return await get_task_result(ticket_id)\r\n\r\n\r\<EMAIL>(\"/status/{ticket_id}\", response_model=TaskStatusResponse)\r\nasync def get_status(ticket_id: str):\r\n    \"\"\"\r\n    获取任务状态（轻量级接口）\r\n\r\n    只返回基本的状态信息，不包含结果数据\r\n    适合频繁轮询使用\r\n    \"\"\"\r\n    return await get_task_status(ticket_id)\r\n\r\n\r\<EMAIL>(\"/terminate/{ticket_id}\", response_model=TerminateResponse)\r\nasync def terminate(ticket_id: str):\r\n    \"\"\"\r\n    终止指定的任务\r\n\r\n    跨副本终止机制：\r\n    1. 本地任务：直接终止进程\r\n    2. 远程任务：标记为取消状态，依赖其他副本检查\r\n    3. 已结束任务：仅更新状态\r\n\r\n    使用两阶段终止：SIGTERM -> SIGKILL\r\n    \"\"\"\r\n    success, message = await terminate_task_process(ticket_id)\r\n    return TerminateResponse(ticket_id=ticket_id, message=message, success=success)\r\n\r\n\r\<EMAIL>(\"/files/{ticket_id}\", response_model=TaskFilesResponse)\r\nasync def get_files(ticket_id: str):\r\n    \"\"\"\r\n    获取任务的所有文件URL列表\r\n\r\n    根据任务ID获取该任务的所有文件URL，返回三个独立的URL列表：\r\n    - video_urls: 视频文件URL列表（通常只有一个元素或为空）\r\n    - screenshots_urls: 截图文件URL列表（按时间顺序排列）\r\n    - file_urls: 下载文件URL列表（按下载顺序排列）\r\n\r\n    数据来源：\r\n    - 从数据库Task模型的 video_file_path、screenshot_file_paths、download_file_paths 字段获取\r\n    - 直接返回数据库中存储的文件路径作为URL，不进行文件验证或预签名处理\r\n\r\n    响应字段：\r\n    - ticket_id: 任务ID\r\n    - video_urls: 视频文件URL列表\r\n    - screenshots_urls: 截图文件URL列表\r\n    - file_urls: 下载文件URL列表\r\n\r\n    Args:\r\n        ticket_id: 任务ID\r\n\r\n    Returns:\r\n        TaskFilesResponse: 包含三个URL列表的任务文件响应\r\n\r\n    Raises:\r\n        HTTPException: 任务不存在时返回404错误\r\n    \"\"\"\r\n    return await get_task_files(ticket_id)\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/api/routes/task.py b/app/api/routes/task.py
--- a/app/api/routes/task.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/api/routes/task.py	(date 1753930319078)
@@ -7,14 +7,14 @@
 from fastapi import APIRouter
 
 from app.schemas.task import (
-    QueryRequest,
-    QueryResponse,
+    TaskExecuteRequest,
+    TaskExecuteResponse,
     TaskFilesResponse,
     TaskResultResponse,
     TaskStatusResponse,
-    TerminateResponse,
+    TaskTerminateResponse,
 )
-from app.services.process_service import start_agent_process, terminate_task_process
+from app.services.task_execution_service import start_agent_process, terminate_task_process
 from app.services.task_service import (
     create_task,
     get_task_files,
@@ -25,8 +25,8 @@
 router = APIRouter()
 
 
-@router.post("/query", response_model=QueryResponse)
-async def query(request: QueryRequest):
+@router.post("/execute", response_model=TaskExecuteResponse)
+async def execute(request: TaskExecuteRequest):
     """
     创建新的浏览器自动化任务
 
@@ -42,7 +42,7 @@
     # 启动任务进程，传递可选的登录凭据和云服务提供商类型
     await start_agent_process(ticket_id, request.task, request.username, request.password, request.provider_type)
 
-    return QueryResponse(ticket_id=ticket_id, status="PENDING", message="任务已创建并开始执行")
+    return TaskExecuteResponse(ticket_id=ticket_id, status="PENDING", message="任务已创建并开始执行")
 
 
 @router.get("/result/{ticket_id}", response_model=TaskResultResponse)
@@ -71,7 +71,7 @@
     return await get_task_status(ticket_id)
 
 
-@router.get("/terminate/{ticket_id}", response_model=TerminateResponse)
+@router.get("/terminate/{ticket_id}", response_model=TaskTerminateResponse)
 async def terminate(ticket_id: str):
     """
     终止指定的任务
@@ -84,7 +84,7 @@
     使用两阶段终止：SIGTERM -> SIGKILL
     """
     success, message = await terminate_task_process(ticket_id)
-    return TerminateResponse(ticket_id=ticket_id, message=message, success=success)
+    return TaskTerminateResponse(ticket_id=ticket_id, message=message, success=success)
 
 
 @router.get("/files/{ticket_id}", response_model=TaskFilesResponse)
Index: app/utils/mq_helper.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\nRabbitMQ消息发送工具模块\r\n\r\n提供统一的消息发送接口，用于子进程中向RabbitMQ发送各种类型的消息\r\n\"\"\"\r\n\r\nfrom typing import Any\r\n\r\nfrom app.services.mq_producer import cleanup_producer, get_producer\r\nfrom app.utils.log import logger\r\n\r\n\r\ndef send_step_log_message(ticket_id: str, message: str, append: bool = True, raise_on_error: bool = False) -> None:\r\n    \"\"\"\r\n    在子进程中发送步骤日志消息到RabbitMQ\r\n\r\n    Args:\r\n        ticket_id: 任务ID\r\n        message: 日志消息\r\n        append: 是否追加到现有日志（True）还是覆盖（False），默认为追加\r\n        raise_on_error: 是否在错误时抛出异常，默认为False（日志更新失败不影响主要逻辑）\r\n    \"\"\"\r\n    try:\r\n        producer = get_producer()\r\n        response = producer.send_append_log(ticket_id=ticket_id, message_text=message, append=append)\r\n\r\n        if response.success:\r\n            logger.debug(f\"子进程发送步骤日志消息成功: {ticket_id}\")\r\n        else:\r\n            logger.error(f\"子进程发送步骤日志消息失败: {ticket_id}, {response.error}\")\r\n            if raise_on_error:\r\n                raise RuntimeError(f\"发送步骤日志消息失败: {response.error}\")\r\n    except Exception as e:\r\n        logger.error(f\"子进程发送步骤日志消息异常: {ticket_id}, {str(e)}\")\r\n        if raise_on_error:\r\n            raise\r\n\r\n\r\ndef send_task_status_message(ticket_id: str, status: str, result_or_error: Any = None, step_log_message: str = None):\r\n    \"\"\"\r\n    在子进程中发送任务状态更新消息到RabbitMQ\r\n\r\n    Args:\r\n        ticket_id: 任务ID\r\n        status: 任务状态 (SUCCESS/FAILURE/CANCELLED/RUNNING)\r\n        result_or_error: 成功时的结果或失败时的错误信息\r\n        step_log_message: 可选的步骤日志消息\r\n\r\n    Raises:\r\n        RuntimeError: 当消息发送失败时\r\n\r\n    Note:\r\n        这是核心的状态更新函数，异常会被抛出给调用方处理\r\n    \"\"\"\r\n    try:\r\n        producer = get_producer()\r\n        response = producer.send_update_status(\r\n            ticket_id=ticket_id, status=status, result_or_error=result_or_error, step_log_message=step_log_message\r\n        )\r\n\r\n        if response.success:\r\n            logger.info(f\"子进程发送任务状态消息成功: {ticket_id} -> {status}\")\r\n        else:\r\n            error_msg = f\"发送任务状态消息失败: {response.error}\"\r\n            logger.error(f\"子进程更新失败 - {error_msg}\")\r\n            raise RuntimeError(error_msg)\r\n\r\n    except Exception as e:\r\n        logger.error(f\"子进程发送状态消息异常: {ticket_id}, {str(e)}\")\r\n        # 重新抛出异常，让调用方知道更新失败\r\n        raise\r\n\r\n\r\ndef send_selenium_session_message(ticket_id: str, session_id: str):\r\n    \"\"\"\r\n    发送Selenium会话消息到RabbitMQ\r\n\r\n    Args:\r\n        ticket_id: 任务ID\r\n        session_id: Selenium会话ID\r\n    \"\"\"\r\n    try:\r\n        producer = get_producer()\r\n        response = producer.send_selenium_session(ticket_id, session_id)\r\n\r\n        if response.success:\r\n            logger.info(f\"子进程发送Selenium会话消息成功: {ticket_id} -> {session_id}\")\r\n        else:\r\n            error_msg = f\"发送Selenium会话消息失败: {response.error}\"\r\n            logger.error(f\"子进程更新失败 - {error_msg}\")\r\n            raise RuntimeError(error_msg)\r\n\r\n    except Exception as e:\r\n        logger.error(f\"子进程发送Selenium会话消息异常: {ticket_id}, {str(e)}\")\r\n        # 重新抛出异常，让调用方知道更新失败\r\n        raise\r\n\r\n\r\ndef send_file_paths_update_message(ticket_id: str, file_paths_data: dict, raise_on_error: bool = False) -> None:\r\n    \"\"\"\r\n    在子进程中发送文件路径更新消息到RabbitMQ\r\n\r\n    Args:\r\n        ticket_id: 任务ID\r\n        file_paths_data: 文件路径数据字典\r\n        raise_on_error: 是否在错误时抛出异常，默认为False\r\n    \"\"\"\r\n    try:\r\n        producer = get_producer()\r\n        response = producer.send_update_file_paths(ticket_id=ticket_id, file_paths_data=file_paths_data)\r\n\r\n        if response.success:\r\n            logger.debug(f\"文件路径更新消息发送成功: {ticket_id}\")\r\n        else:\r\n            error_msg = f\"文件路径更新消息发送失败: {ticket_id}, 错误: {response.error}\"\r\n            logger.error(error_msg)\r\n            if raise_on_error:\r\n                raise Exception(error_msg)\r\n\r\n    except Exception as e:\r\n        error_msg = f\"发送文件路径更新消息时出错: {ticket_id}, 错误: {str(e)}\"\r\n        logger.error(error_msg)\r\n        if raise_on_error:\r\n            raise\r\n\r\n\r\ndef send_append_screenshot_path_message(ticket_id: str, screenshot_path: str, raise_on_error: bool = False) -> None:\r\n    \"\"\"\r\n    在子进程中发送追加截图路径消息到RabbitMQ\r\n\r\n    Args:\r\n        ticket_id: 任务ID\r\n        screenshot_path: 截图文件路径\r\n        raise_on_error: 是否在错误时抛出异常，默认为False\r\n    \"\"\"\r\n    try:\r\n        producer = get_producer()\r\n        response = producer.send_append_screenshot_path(ticket_id=ticket_id, screenshot_path=screenshot_path)\r\n\r\n        if response.success:\r\n            logger.debug(f\"追加截图路径消息发送成功: {ticket_id} -> {screenshot_path}\")\r\n        else:\r\n            error_msg = f\"追加截图路径消息发送失败: {ticket_id}, 错误: {response.error}\"\r\n            logger.error(error_msg)\r\n            if raise_on_error:\r\n                raise Exception(error_msg)\r\n\r\n    except Exception as e:\r\n        error_msg = f\"发送追加截图路径消息时出错: {ticket_id}, 错误: {str(e)}\"\r\n        logger.error(error_msg)\r\n        if raise_on_error:\r\n            raise\r\n\r\n\r\ndef cleanup_mq_producer() -> None:\r\n    \"\"\"\r\n    清理RabbitMQ生产者资源\r\n\r\n    用于子进程结束时清理资源\r\n    \"\"\"\r\n    try:\r\n        cleanup_producer()\r\n        logger.debug(\"RabbitMQ生产者清理完成\")\r\n    except Exception as e:\r\n        logger.debug(f\"清理RabbitMQ生产者时出错: {str(e)}\")\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/utils/mq_helper.py b/app/utils/mq_helper.py
--- a/app/utils/mq_helper.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/utils/mq_helper.py	(date 1754012034417)
@@ -6,6 +6,7 @@
 
 from typing import Any
 
+from app.config.enums import ExecutionStatus
 from app.services.mq_producer import cleanup_producer, get_producer
 from app.utils.log import logger
 
@@ -36,13 +37,15 @@
             raise
 
 
-def send_task_status_message(ticket_id: str, status: str, result_or_error: Any = None, step_log_message: str = None):
+def send_task_status_message(
+    ticket_id: str, status: ExecutionStatus, result_or_error: Any = None, step_log_message: str = None
+):
     """
     在子进程中发送任务状态更新消息到RabbitMQ
 
     Args:
         ticket_id: 任务ID
-        status: 任务状态 (SUCCESS/FAILURE/CANCELLED/RUNNING)
+        status: 任务状态
         result_or_error: 成功时的结果或失败时的错误信息
         step_log_message: 可选的步骤日志消息
 
Index: app/services/task_service.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\n任务业务逻辑服务\r\n\r\n负责任务的创建、查询、管理等业务逻辑\r\n\"\"\"\r\n\r\nimport uuid\r\n\r\nfrom fastapi import HTTPException\r\n\r\nfrom app.config.settings import settings\r\nfrom app.models.task import Task\r\nfrom app.schemas.task import (\r\n    TaskFilesResponse,\r\n    TaskResultResponse,\r\n    TaskStatusResponse,\r\n)\r\nfrom app.utils.timezone import get_shanghai_now\r\n\r\n\r\nasync def create_task(task_description: str) -> str:\r\n    \"\"\"\r\n    创建新任务\r\n\r\n    Args:\r\n        task_description: 任务描述\r\n\r\n    Returns:\r\n        str: 任务ID\r\n    \"\"\"\r\n    ticket_id = str(uuid.uuid4().hex)\r\n    now = get_shanghai_now()\r\n\r\n    await Task.create(\r\n        ticket_id=ticket_id,\r\n        status=\"PENDING\",\r\n        task_description=task_description,\r\n        replica_id=settings.replica_id,\r\n        created_at=now,\r\n        updated_at=now,\r\n    )\r\n\r\n    return ticket_id\r\n\r\n\r\nasync def get_task_result(ticket_id: str) -> TaskResultResponse:\r\n    \"\"\"\r\n    获取任务的详细结果\r\n\r\n    Args:\r\n        ticket_id: 任务ID\r\n\r\n    Returns:\r\n        TaskResultResponse: 任务结果响应\r\n    \"\"\"\r\n    task = await Task.get_or_none(ticket_id=ticket_id)\r\n\r\n    if not task:\r\n        return TaskResultResponse(ticket_id=ticket_id, status=\"NOT_FOUND\", result=None, error_message=\"任务不存在\")\r\n\r\n    return TaskResultResponse(\r\n        ticket_id=task.ticket_id,\r\n        status=task.status,\r\n        step_log=task.step_log,\r\n        result=task.result,\r\n        error_message=task.error_message,\r\n        selenium_session_id=task.selenium_session_id,\r\n        replica_id=task.replica_id,\r\n        created_at=task.created_at,\r\n        updated_at=task.updated_at,\r\n        video_file_paths=task.video_file_paths,\r\n        screenshot_file_paths=task.screenshot_file_paths,\r\n        download_file_paths=task.download_file_paths,\r\n    )\r\n\r\n\r\nasync def get_task_status(ticket_id: str) -> TaskStatusResponse:\r\n    \"\"\"\r\n    获取任务状态（轻量级接口）\r\n\r\n    Args:\r\n        ticket_id: 任务ID\r\n\r\n    Returns:\r\n        TaskStatusResponse: 任务状态响应\r\n    \"\"\"\r\n    task = await Task.get_or_none(ticket_id=ticket_id)\r\n\r\n    if not task:\r\n        return TaskStatusResponse(ticket_id=ticket_id, status=\"NOT_FOUND\")\r\n\r\n    return TaskStatusResponse(ticket_id=task.ticket_id, status=task.status, replica_id=task.replica_id)\r\n\r\n\r\nasync def get_task_files(ticket_id: str) -> TaskFilesResponse:\r\n    \"\"\"\r\n    获取任务的所有文件信息\r\n\r\n    直接从数据库返回文件路径作为URL列表，不进行文件验证和详细信息获取\r\n\r\n    Args:\r\n        ticket_id: 任务ID\r\n\r\n    Returns:\r\n        TaskFilesResponse: 任务文件响应，包含三个URL列表\r\n\r\n    Raises:\r\n        HTTPException: 任务不存在时抛出404错误\r\n    \"\"\"\r\n    # 查询任务是否存在\r\n    task = await Task.get_or_none(ticket_id=ticket_id)\r\n    if not task:\r\n        raise HTTPException(status_code=404, detail=\"任务不存在\")\r\n\r\n    # 处理视频文件URL列表\r\n    video_urls = []\r\n    if task.video_file_paths:\r\n        video_urls = list(task.video_file_paths)\r\n\r\n    # 处理截图文件URL列表\r\n    screenshots_urls = []\r\n    if task.screenshot_file_paths:\r\n        screenshots_urls = list(task.screenshot_file_paths)\r\n\r\n    # 处理下载文件URL列表\r\n    file_urls = []\r\n    if task.download_file_paths:\r\n        file_urls = list(task.download_file_paths)\r\n\r\n    return TaskFilesResponse(\r\n        ticket_id=ticket_id,\r\n        video_urls=video_urls,\r\n        screenshots_urls=screenshots_urls,\r\n        file_urls=file_urls,\r\n    )\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/services/task_service.py b/app/services/task_service.py
--- a/app/services/task_service.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/services/task_service.py	(date 1754011719803)
@@ -8,6 +8,7 @@
 
 from fastapi import HTTPException
 
+from app.config.enums import ExecutionStatus
 from app.config.settings import settings
 from app.models.task import Task
 from app.schemas.task import (
@@ -33,7 +34,7 @@
 
     await Task.create(
         ticket_id=ticket_id,
-        status="PENDING",
+        status=ExecutionStatus.PENDING,
         task_description=task_description,
         replica_id=settings.replica_id,
         created_at=now,
@@ -87,7 +88,7 @@
     task = await Task.get_or_none(ticket_id=ticket_id)
 
     if not task:
-        return TaskStatusResponse(ticket_id=ticket_id, status="NOT_FOUND")
+        raise HTTPException(status_code=404, detail="任务不存在")
 
     return TaskStatusResponse(ticket_id=task.ticket_id, status=task.status, replica_id=task.replica_id)
 
Index: app/services/process_service.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/services/process_service.py b/app/services/task_execution_service.py
rename from app/services/process_service.py
rename to app/services/task_execution_service.py
--- a/app/services/process_service.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/services/task_execution_service.py	(date 1754011854035)
@@ -11,6 +11,7 @@
 from browser_use import Agent, BrowserSession
 from PIL import Image
 
+from app.config.enums import ExecutionStatus
 from app.config.settings import settings
 from app.models.task import Task
 from app.utils.browser_use_deepseek import BrowserUseDeepSeek
@@ -401,7 +402,7 @@
                 now = get_shanghai_now()
                 loop.run_until_complete(
                     Task.filter(ticket_id=ticket_id).update(
-                        status="FAILURE",
+                        status=ExecutionStatus.FAILED,
                         error_message=f"状态更新失败: {str(status_update_error)}",
                         completed_at=now,
                         updated_at=now,
@@ -515,7 +516,7 @@
                 break
 
             # 检查是否被取消
-            if task.status == "CANCELLED":
+            if task.status == ExecutionStatus.CANCELLED:
                 logger.info(f"监控任务 {ticket_id} - 任务已被取消，正在终止进程")
                 await terminate_process(process)
                 break
@@ -530,7 +531,7 @@
 
                     # 标记任务为超时失败
                     await Task.filter(ticket_id=ticket_id).update(
-                        status="FAILURE",
+                        status=ExecutionStatus.FAILED,
                         error_message=f"任务执行超时（超过{settings.task_timeout_minutes}分钟）",
                         completed_at=now,
                         updated_at=now,
@@ -549,7 +550,7 @@
         try:
             now = get_shanghai_now()
             await Task.filter(ticket_id=ticket_id).update(
-                status="FAILURE",
+                status=ExecutionStatus.FAILED,
                 error_message=f"监控进程时出错: {str(e)}",
                 completed_at=now,
                 updated_at=now,
@@ -582,7 +583,7 @@
         # 标记任务为取消状态
         now = get_shanghai_now()
         await Task.filter(ticket_id=ticket_id).update(
-            status="CANCELLED",
+            status=ExecutionStatus.CANCELLED,
             error_message="任务被手动取消",
             completed_at=now,
             updated_at=now,
Index: app/api/dependencies.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\nAPI 依赖注入\r\n\r\n定义 FastAPI 路由中使用的依赖项\r\n\"\"\"\r\n\r\nfrom tortoise import Tortoise\r\nfrom tortoise.contrib.fastapi import register_tortoise\r\n\r\nfrom app.config.settings import settings\r\n\r\n\r\ndef get_database_config():\r\n    \"\"\"\r\n    获取数据库配置字典\r\n\r\n    Returns:\r\n        dict: Tortoise ORM 配置字典\r\n    \"\"\"\r\n    return {\r\n        \"connections\": {\"default\": settings.database_url},\r\n        \"apps\": {\r\n            \"models\": {\r\n                \"models\": [\"app.models.task\"],\r\n                \"default_connection\": \"default\",\r\n            }\r\n        },\r\n        \"use_tz\": False,\r\n        \"timezone\": settings.database_timezone,\r\n    }\r\n\r\n\r\nasync def init_database_for_subprocess():\r\n    \"\"\"\r\n    为子进程初始化数据库连接\r\n\r\n    使用 Tortoise.init() 而不是 register_tortoise()\r\n    因为子进程中没有 FastAPI 应用实例\r\n    \"\"\"\r\n    await Tortoise.init(config=get_database_config())\r\n\r\n\r\ndef init_database(app):\r\n    \"\"\"\r\n    为 FastAPI 应用初始化数据库连接\r\n\r\n    Args:\r\n        app: FastAPI 应用实例\r\n    \"\"\"\r\n    register_tortoise(\r\n        app,\r\n        config=get_database_config(),\r\n        generate_schemas=True,  # 自动创建表结构\r\n        add_exception_handlers=True,  # 添加异常处理\r\n    )\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/api/dependencies.py b/app/api/dependencies.py
--- a/app/api/dependencies.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/api/dependencies.py	(date 1753751441915)
@@ -21,7 +21,7 @@
         "connections": {"default": settings.database_url},
         "apps": {
             "models": {
-                "models": ["app.models.task"],
+                "models": ["app.models.task", "app.models.script"],
                 "default_connection": "default",
             }
         },
Index: app/schemas/mq_message.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\nRabbitMQ消息模型定义\r\n\r\n定义数据库操作消息的结构和类型\r\n\"\"\"\r\n\r\nfrom datetime import datetime\r\nfrom enum import Enum\r\nfrom typing import Any\r\n\r\nfrom pydantic import BaseModel, Field\r\n\r\nfrom app.utils.timezone import get_shanghai_now\r\n\r\n\r\nclass DatabaseOperationType(str, Enum):\r\n    \"\"\"数据库操作类型枚举\"\"\"\r\n\r\n    UPDATE_STATUS = \"update_status\"  # 更新任务状态\r\n    APPEND_LOG = \"append_log\"  # 追加步骤日志\r\n    UPDATE_RESULT = \"update_result\"  # 更新执行结果\r\n    UPDATE_ERROR = \"update_error\"  # 更新错误信息\r\n    UPDATE_SELENIUM_SESSION = \"update_selenium_session\"  # 更新Selenium会话\r\n    UPDATE_FILE_PATHS = \"update_file_paths\"  # 更新文件路径\r\n    APPEND_SCREENSHOT_PATH = \"append_screenshot_path\"  # 追加截图路径\r\n\r\n\r\nclass DatabaseMessage(BaseModel):\r\n    \"\"\"数据库操作消息\"\"\"\r\n\r\n    # 消息ID，用于幂等性控制\r\n    message_id: str = Field(..., description=\"消息唯一标识\")\r\n\r\n    # 操作类型\r\n    operation_type: DatabaseOperationType = Field(..., description=\"数据库操作类型\")\r\n\r\n    # 任务ID\r\n    ticket_id: str = Field(..., description=\"任务ID\")\r\n\r\n    # 操作数据，根据操作类型有不同的结构\r\n    data: dict = Field(..., description=\"操作数据\")\r\n\r\n    # 消息创建时间\r\n    created_at: datetime = Field(default_factory=lambda: get_shanghai_now(), description=\"消息创建时间\")\r\n\r\n    # 重试次数\r\n    retry_count: int = Field(default=0, description=\"当前重试次数\")\r\n\r\n    # 最大重试次数\r\n    max_retry: int = Field(default=3, description=\"最大重试次数\")\r\n\r\n\r\nclass UpdateStatusMessage(BaseModel):\r\n    \"\"\"更新状态消息数据\"\"\"\r\n\r\n    status: str = Field(..., description=\"任务状态\")\r\n    result_or_error: Any | None = Field(None, description=\"结果或错误信息\")\r\n    step_log_message: str | None = Field(None, description=\"步骤日志消息\")\r\n\r\n\r\nclass UpdateSeleniumSessionMessage(BaseModel):\r\n    \"\"\"Selenium会话消息数据\"\"\"\r\n\r\n    session_id: str = Field(..., description=\"Selenium会话ID\")\r\n\r\n\r\nclass AppendLogMessage(BaseModel):\r\n    \"\"\"追加日志消息数据\"\"\"\r\n\r\n    message: str = Field(..., description=\"日志消息\")\r\n    append: bool = Field(default=True, description=\"是否追加\")\r\n\r\n\r\nclass UpdateResultMessage(BaseModel):\r\n    \"\"\"更新结果消息数据\"\"\"\r\n\r\n    result: Any = Field(..., description=\"执行结果\")\r\n\r\n\r\nclass UpdateErrorMessage(BaseModel):\r\n    \"\"\"更新错误消息数据\"\"\"\r\n\r\n    error_message: str = Field(..., description=\"错误信息\")\r\n\r\n\r\nclass UpdateFilePathsMessage(BaseModel):\r\n    \"\"\"更新文件路径消息数据\"\"\"\r\n\r\n    video_file_paths: list[str] | None = Field(None, description=\"视频文件路径列表\")\r\n    screenshot_file_paths: list[str] | None = Field(None, description=\"截图文件路径列表\")\r\n    download_file_paths: list[str] | None = Field(None, description=\"下载文件路径列表\")\r\n\r\n\r\nclass AppendScreenshotPathMessage(BaseModel):\r\n    \"\"\"追加截图路径消息数据\"\"\"\r\n\r\n    screenshot_path: str = Field(..., description=\"截图文件路径\")\r\n\r\n\r\nclass MessageResponse(BaseModel):\r\n    \"\"\"消息发送响应\"\"\"\r\n\r\n    success: bool = Field(..., description=\"是否成功\")\r\n    message_id: str | None = Field(None, description=\"消息ID\")\r\n    error: str | None = Field(None, description=\"错误信息\")\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/schemas/mq_message.py b/app/schemas/mq_message.py
--- a/app/schemas/mq_message.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/schemas/mq_message.py	(date 1754012076083)
@@ -10,6 +10,7 @@
 
 from pydantic import BaseModel, Field
 
+from app.config.enums import ExecutionStatus
 from app.utils.timezone import get_shanghai_now
 
 
@@ -53,7 +54,7 @@
 class UpdateStatusMessage(BaseModel):
     """更新状态消息数据"""
 
-    status: str = Field(..., description="任务状态")
+    status: ExecutionStatus = Field(..., description="任务状态")
     result_or_error: Any | None = Field(None, description="结果或错误信息")
     step_log_message: str | None = Field(None, description="步骤日志消息")
 
Index: app/main.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\nBrowser Use FastAPI 多副本服务主应用\r\n\r\n这是重构后的主应用文件，采用了更清晰的项目结构\r\n\"\"\"\r\n\r\nfrom contextlib import asynccontextmanager\r\n\r\nfrom dotenv import load_dotenv\r\n\r\n# 首先加载环境变量\r\nload_dotenv(override=True)\r\n\r\nfrom fastapi import FastAPI\r\n\r\nfrom app.api.dependencies import init_database\r\nfrom app.api.routes.task import router as task_router\r\nfrom app.config.settings import settings\r\nfrom app.services.mq_consumer import start_global_consumer, stop_global_consumer\r\nfrom app.services.process_service import local_processes, terminate_task_process\r\nfrom app.utils.log import logger\r\n\r\n\r\n@asynccontextmanager\r\nasync def lifespan(app: FastAPI):\r\n    \"\"\"应用生命周期管理\"\"\"\r\n    # 启动时的初始化\r\n    logger.info(\"启动 RabbitMQ 消费者...\")\r\n    try:\r\n        start_global_consumer()\r\n        logger.info(\"RabbitMQ 消费者启动成功\")\r\n    except Exception as e:\r\n        logger.error(f\"启动 RabbitMQ 消费者失败: {str(e)}\")\r\n        raise\r\n\r\n    yield\r\n\r\n    # 关闭所有子进程\r\n    logger.info(\"关闭 所有子进程\")\r\n    local_processes_keys = list(local_processes.keys())\r\n    for ticket_id in local_processes_keys:\r\n        await terminate_task_process(ticket_id)\r\n    # 关闭时的清理\r\n    logger.info(\"关闭 RabbitMQ 消费者...\")\r\n    try:\r\n        stop_global_consumer()\r\n        logger.info(\"RabbitMQ 消费者关闭成功\")\r\n    except Exception as e:\r\n        logger.error(f\"关闭 RabbitMQ 消费者失败: {str(e)}\")\r\n\r\n\r\n# 记录应用启动\r\nlogger.info(\"初始化 FastAPI 应用\")\r\n\r\n# 创建 FastAPI 应用实例\r\napp = FastAPI(\r\n    title=settings.app_title,\r\n    version=settings.app_version,\r\n    description=\"WebUI智能体执行服务\",\r\n    lifespan=lifespan,\r\n    openapi_url=settings.openapi_url,\r\n)\r\n\r\n# 初始化数据库\r\ninit_database(app)\r\n\r\n# 注册路由\r\napp.include_router(task_router, prefix=\"/v1\", tags=[\"task\"])\r\n\r\nlogger.info(f\"应用初始化完成 - 副本ID: {settings.replica_id}\")\r\n\r\n\r\<EMAIL>(\"/\")\r\nasync def root():\r\n    \"\"\"根路径，返回服务信息\"\"\"\r\n    return {\r\n        \"service\": settings.app_title,\r\n        \"version\": settings.app_version,\r\n        \"replica_id\": settings.replica_id,\r\n        \"status\": \"running\",\r\n    }\r\n\r\n\r\<EMAIL>(\"/health\")\r\nasync def health():\r\n    \"\"\"健康检查接口\"\"\"\r\n    return {\"status\": \"healthy\", \"replica_id\": settings.replica_id}\r\n\r\n\r\nif __name__ == \"__main__\":\r\n    import uvicorn\r\n\r\n    # 启动 FastAPI 服务\r\n    uvicorn.run(app, host=settings.host, port=settings.port)\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/main.py b/app/main.py
--- a/app/main.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/main.py	(date 1753931262869)
@@ -14,10 +14,12 @@
 from fastapi import FastAPI
 
 from app.api.dependencies import init_database
+from app.api.routes.script import router as script_router
 from app.api.routes.task import router as task_router
 from app.config.settings import settings
 from app.services.mq_consumer import start_global_consumer, stop_global_consumer
-from app.services.process_service import local_processes, terminate_task_process
+from app.services.script_execution_service import script_processes, terminate_script_process
+from app.services.task_execution_service import local_processes, terminate_task_process
 from app.utils.log import logger
 
 
@@ -35,11 +37,18 @@
 
     yield
 
-    # 关闭所有子进程
-    logger.info("关闭 所有子进程")
+    # 关闭所有任务进程
+    logger.info("关闭所有任务进程")
     local_processes_keys = list(local_processes.keys())
     for ticket_id in local_processes_keys:
         await terminate_task_process(ticket_id)
+
+    # 关闭所有脚本进程
+    logger.info("关闭所有脚本进程")
+    script_processes_keys = list(script_processes.keys())
+    for ticket_id in script_processes_keys:
+        await terminate_script_process(ticket_id)
+
     # 关闭时的清理
     logger.info("关闭 RabbitMQ 消费者...")
     try:
@@ -65,7 +74,8 @@
 init_database(app)
 
 # 注册路由
-app.include_router(task_router, prefix="/v1", tags=["task"])
+app.include_router(task_router, prefix="/v1/task", tags=["task"])
+app.include_router(script_router, prefix="/v1/script", tags=["script"])
 
 logger.info(f"应用初始化完成 - 副本ID: {settings.replica_id}")
 
Index: app/schemas/__init__.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"Pydantic 模型\"\"\"\r\n\r\nfrom .mq_message import (\r\n    AppendLogMessage,\r\n    AppendScreenshotPathMessage,\r\n    DatabaseMessage,\r\n    DatabaseOperationType,\r\n    MessageResponse,\r\n    UpdateErrorMessage,\r\n    UpdateFilePathsMessage,\r\n    UpdateResultMessage,\r\n    UpdateSeleniumSessionMessage,\r\n    UpdateStatusMessage,\r\n)\r\nfrom .task import (\r\n    QueryRequest,\r\n    QueryResponse,\r\n    TaskResultResponse,\r\n    TaskStatusResponse,\r\n    TerminateResponse,\r\n)\r\n\r\n__all__ = [\r\n    # MQ Message schemas\r\n    \"AppendLogMessage\",\r\n    \"AppendScreenshotPathMessage\",\r\n    \"DatabaseMessage\",\r\n    \"DatabaseOperationType\",\r\n    \"MessageResponse\",\r\n    \"UpdateErrorMessage\",\r\n    \"UpdateFilePathsMessage\",\r\n    \"UpdateResultMessage\",\r\n    \"UpdateSeleniumSessionMessage\",\r\n    \"UpdateStatusMessage\",\r\n    # Task schemas\r\n    \"QueryRequest\",\r\n    \"QueryResponse\",\r\n    \"TaskResultResponse\",\r\n    \"TaskStatusResponse\",\r\n    \"TerminateResponse\",\r\n]\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/schemas/__init__.py b/app/schemas/__init__.py
--- a/app/schemas/__init__.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/schemas/__init__.py	(date 1753857269866)
@@ -12,12 +12,19 @@
     UpdateSeleniumSessionMessage,
     UpdateStatusMessage,
 )
+from .script import (
+    ScriptExecuteRequest,
+    ScriptExecuteResponse,
+    ScriptResultResponse,
+    ScriptStatusResponse,
+    ScriptTerminateResponse,
+)
 from .task import (
-    QueryRequest,
-    QueryResponse,
+    TaskExecuteRequest,
+    TaskExecuteResponse,
     TaskResultResponse,
     TaskStatusResponse,
-    TerminateResponse,
+    TaskTerminateResponse,
 )
 
 __all__ = [
@@ -32,10 +39,16 @@
     "UpdateResultMessage",
     "UpdateSeleniumSessionMessage",
     "UpdateStatusMessage",
+    # Script schemas
+    "ScriptExecuteRequest",
+    "ScriptExecuteResponse",
+    "ScriptResultResponse",
+    "ScriptStatusResponse",
+    "ScriptTerminateResponse",
     # Task schemas
-    "QueryRequest",
-    "QueryResponse",
+    "TaskExecuteRequest",
+    "TaskExecuteResponse",
     "TaskResultResponse",
     "TaskStatusResponse",
-    "TerminateResponse",
+    "TaskTerminateResponse",
 ]
Index: app/schemas/task.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\n任务相关的 Pydantic 模型\r\n\r\n定义 API 请求和响应的数据结构\r\n\"\"\"\r\n\r\nfrom datetime import datetime\r\n\r\nfrom pydantic import BaseModel, Field, field_validator\r\n\r\n\r\nclass QueryRequest(BaseModel):\r\n    \"\"\"创建任务的请求模型\"\"\"\r\n\r\n    task: str = Field(..., description=\"用户输入的任务描述\")\r\n    username: str | None = Field(None, description=\"可选的登录用户名\")\r\n    password: str | None = Field(None, description=\"可选的登录密码\")\r\n    provider_type: str | None = Field(\r\n        None,\r\n        description=\"可选的云服务提供商类型，支持: ecloud(移动云), baidu_cloud(百度云), huawei_cloud(华为云), alibaba_cloud(阿里云), default(默认)\",\r\n    )\r\n\r\n    @field_validator(\"task\")\r\n    @classmethod\r\n    def validate_task(cls, value: str) -> str:\r\n        \"\"\"验证任务描述不能为空\"\"\"\r\n        if not value or not value.strip():\r\n            raise ValueError(\"任务描述不能为空\")\r\n        return value.strip()\r\n\r\n    @field_validator(\"provider_type\")\r\n    @classmethod\r\n    def validate_provider_type(cls, value: str | None) -> str | None:\r\n        \"\"\"验证云服务提供商类型\"\"\"\r\n        if value is None:\r\n            return value\r\n\r\n        # 支持的云服务提供商类型\r\n        valid_types = [\"ecloud\", \"baidu_cloud\", \"huawei_cloud\", \"alibaba_cloud\", \"default\"]\r\n\r\n        if value.lower() not in valid_types:\r\n            raise ValueError(f\"不支持的云服务提供商类型: {value}，支持的类型: {', '.join(valid_types)}\")\r\n\r\n        return value.lower()\r\n\r\n\r\nclass QueryResponse(BaseModel):\r\n    \"\"\"创建任务的响应模型\"\"\"\r\n\r\n    ticket_id: str  # 生成的任务ID\r\n    status: str  # 当前状态\r\n    message: str  # 状态描述信息\r\n\r\n\r\nclass TaskResultResponse(BaseModel):\r\n    \"\"\"获取任务结果的响应模型\"\"\"\r\n\r\n    ticket_id: str  # 任务ID\r\n    status: str  # 任务状态\r\n    step_log: str | None = None  # 任务执行步骤日志\r\n    result: list | None = None  # 成功时的结果数据\r\n    error_message: str | None = None  # 失败时的错误信息\r\n    selenium_session_id: str | None = None  # Selenium会话ID\r\n    replica_id: str | None = None  # 执行该任务的副本ID\r\n    created_at: datetime | None = None  # 创建时间\r\n    updated_at: datetime | None = None  # 更新时间\r\n\r\n    # 文件存储相关字段\r\n    video_file_paths: list[str] | None = None  # 视频文件路径列表\r\n    screenshot_file_paths: list[str] | None = None  # 截图文件路径列表\r\n    download_file_paths: list[str] | None = None  # 下载文件路径列表\r\n\r\n\r\nclass TaskStatusResponse(BaseModel):\r\n    \"\"\"获取任务状态的响应模型（轻量级）\"\"\"\r\n\r\n    ticket_id: str  # 任务ID\r\n    status: str  # 任务状态\r\n    replica_id: str | None = None  # 执行该任务的副本ID\r\n\r\n\r\nclass TerminateResponse(BaseModel):\r\n    \"\"\"终止任务的响应模型\"\"\"\r\n\r\n    ticket_id: str  # 任务ID\r\n    message: str  # 操作结果描述\r\n    success: bool  # 是否成功终止\r\n\r\n\r\nclass TaskFilesResponse(BaseModel):\r\n    \"\"\"获取任务文件的响应模型\"\"\"\r\n\r\n    ticket_id: str = Field(..., description=\"任务ID\")\r\n    video_urls: list[str] = Field(default_factory=list, description=\"视频文件URL列表\")\r\n    screenshots_urls: list[str] = Field(default_factory=list, description=\"截图文件URL列表\")\r\n    file_urls: list[str] = Field(default_factory=list, description=\"下载文件URL列表\")\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/schemas/task.py b/app/schemas/task.py
--- a/app/schemas/task.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/schemas/task.py	(date 1754011608911)
@@ -8,9 +8,11 @@
 
 from pydantic import BaseModel, Field, field_validator
 
+from app.config.enums import ExecutionStatus
 
-class QueryRequest(BaseModel):
-    """创建任务的请求模型"""
+
+class TaskExecuteRequest(BaseModel):
+    """创建任务的执行请求模型"""
 
     task: str = Field(..., description="用户输入的任务描述")
     username: str | None = Field(None, description="可选的登录用户名")
@@ -44,8 +46,8 @@
         return value.lower()
 
 
-class QueryResponse(BaseModel):
-    """创建任务的响应模型"""
+class TaskExecuteResponse(BaseModel):
+    """创建任务的执行响应模型"""
 
     ticket_id: str  # 生成的任务ID
     status: str  # 当前状态
@@ -56,7 +58,7 @@
     """获取任务结果的响应模型"""
 
     ticket_id: str  # 任务ID
-    status: str  # 任务状态
+    status: ExecutionStatus | None = None  # 任务状态
     step_log: str | None = None  # 任务执行步骤日志
     result: list | None = None  # 成功时的结果数据
     error_message: str | None = None  # 失败时的错误信息
@@ -75,11 +77,11 @@
     """获取任务状态的响应模型（轻量级）"""
 
     ticket_id: str  # 任务ID
-    status: str  # 任务状态
+    status: ExecutionStatus | None = None  # 任务状态
     replica_id: str | None = None  # 执行该任务的副本ID
 
 
-class TerminateResponse(BaseModel):
+class TaskTerminateResponse(BaseModel):
     """终止任务的响应模型"""
 
     ticket_id: str  # 任务ID
Index: app/services/callback_service.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\n回调服务\r\n\r\n用于向主服务发送任务执行结果的回调通知\r\n\"\"\"\r\n\r\nimport requests\r\n\r\nfrom app.config.settings import settings\r\nfrom app.utils.log import logger\r\n\r\n\r\nclass CallbackService:\r\n    \"\"\"回调服务类\"\"\"\r\n\r\n    def __init__(self):\r\n        self.timeout = 10.0  # 10秒超时\r\n\r\n    def send_task_callback(\r\n        self,\r\n        ticket_id: str,\r\n        status: str,\r\n        error_message: str | None = None,\r\n    ) -> bool:\r\n        \"\"\"\r\n        发送任务执行结果回调\r\n\r\n        Args:\r\n            ticket_id: 任务票据ID\r\n            status: 任务状态 (SUCCESS, FAILURE, CANCELLED)\r\n            error_message: 错误信息（仅当状态为FAILURE时）\r\n\r\n        Returns:\r\n            bool: 回调是否成功发送\r\n        \"\"\"\r\n        try:\r\n            # 构建回调URL\r\n            callback_url = f\"{settings.webui_agent_url}/task/callback\"\r\n\r\n            # 构建回调数据\r\n            callback_data = {\r\n                \"ticket_id\": ticket_id,\r\n                \"status\": status,\r\n            }\r\n\r\n            # 添加可选字段\r\n            if error_message:\r\n                callback_data[\"error_message\"] = error_message\r\n\r\n            logger.info(f\"发送任务回调: {callback_url}, 数据: {callback_data}\")\r\n\r\n            # 发送回调请求\r\n            response = requests.post(\r\n                callback_url, json=callback_data, headers={\"Content-Type\": \"application/json\"}, timeout=self.timeout\r\n            )\r\n\r\n            # 记录响应详情用于调试\r\n            logger.debug(f\"回调响应状态码: {response.status_code}\")\r\n            logger.debug(f\"回调响应内容: {response.text}\")\r\n\r\n            # 检查响应\r\n            if response.status_code == 200:\r\n                try:\r\n                    response_data = response.json()\r\n                    code = response_data.get(\"code\", 5000)\r\n                    response_message = response_data.get(\"message\", \"未知错误\")\r\n\r\n                    if code == 2000:\r\n                        logger.info(f\"任务回调发送成功: {ticket_id}, 状态: {status}, 消息: {response_message}\")\r\n                        return True\r\n                    else:\r\n                        logger.error(f\"任务回调处理失败: {ticket_id}, 状态: {status}, 消息: {response_message}\")\r\n                        return False\r\n                except (ValueError, TypeError):\r\n                    # 如果响应不是有效的JSON，但HTTP状态码是200，则认为成功\r\n                    logger.info(f\"任务回调发送成功: {ticket_id}, 状态: {status} (响应不是JSON格式)\")\r\n                    return True\r\n            else:\r\n                logger.error(f\"任务回调请求失败: {ticket_id}, HTTP状态: {response.status_code}, 响应: {response.text}\")\r\n                return False\r\n\r\n        except requests.exceptions.Timeout:\r\n            logger.error(f\"任务回调请求超时: {ticket_id}\")\r\n            return False\r\n        except requests.exceptions.RequestException as e:\r\n            logger.error(f\"任务回调请求异常: {ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n        except Exception as e:\r\n            logger.error(f\"任务回调发送异常: {ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def send_task_callback_sync(\r\n        self,\r\n        ticket_id: str,\r\n        status: str,\r\n        error_message: str | None = None,\r\n    ) -> bool:\r\n        \"\"\"\r\n        同步方式发送任务执行结果回调\r\n\r\n        这里直接调用同步方法，为了保持接口一致性\r\n        \"\"\"\r\n        return self.send_task_callback(ticket_id, status, error_message)\r\n\r\n\r\n# 全局回调服务实例\r\n_global_callback_service: CallbackService | None = None\r\n\r\n\r\ndef get_callback_service() -> CallbackService:\r\n    \"\"\"获取全局回调服务实例\"\"\"\r\n    global _global_callback_service\r\n    if _global_callback_service is None:\r\n        _global_callback_service = CallbackService()\r\n    return _global_callback_service\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/services/callback_service.py b/app/services/callback_service.py
--- a/app/services/callback_service.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/services/callback_service.py	(date 1753960944723)
@@ -18,6 +18,7 @@
 
     def send_task_callback(
         self,
+        type: str,
         ticket_id: str,
         status: str,
         error_message: str | None = None,
@@ -26,8 +27,9 @@
         发送任务执行结果回调
 
         Args:
+            type: 回调类型 (task, script)
             ticket_id: 任务票据ID
-            status: 任务状态 (SUCCESS, FAILURE, CANCELLED)
+            status: 任务状态 (SUCCESS, FAILURE)
             error_message: 错误信息（仅当状态为FAILURE时）
 
         Returns:
@@ -35,7 +37,7 @@
         """
         try:
             # 构建回调URL
-            callback_url = f"{settings.webui_agent_url}/task/callback"
+            callback_url = f"{settings.webui_agent_url}/{type}/callback"
 
             # 构建回调数据
             callback_data = {
@@ -91,6 +93,7 @@
 
     def send_task_callback_sync(
         self,
+        type: str,
         ticket_id: str,
         status: str,
         error_message: str | None = None,
@@ -100,7 +103,7 @@
 
         这里直接调用同步方法，为了保持接口一致性
         """
-        return self.send_task_callback(ticket_id, status, error_message)
+        return self.send_task_callback(type, ticket_id, status, error_message)
 
 
 # 全局回调服务实例
Index: app/services/mq_producer.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\nRabbitMQ消息生产者服务\r\n\r\n负责发送数据库操作消息到RabbitMQ\r\n\"\"\"\r\n\r\nimport uuid\r\nfrom typing import Any\r\n\r\nimport pika\r\nfrom pika.adapters.blocking_connection import BlockingChannel\r\nfrom pika.exceptions import AMQPChannelError, AMQPConnectionError\r\n\r\nfrom app.config.settings import settings\r\nfrom app.schemas.mq_message import (\r\n    AppendLogMessage,\r\n    AppendScreenshotPathMessage,\r\n    DatabaseMessage,\r\n    DatabaseOperationType,\r\n    MessageResponse,\r\n    UpdateErrorMessage,\r\n    UpdateFilePathsMessage,\r\n    UpdateResultMessage,\r\n    UpdateSeleniumSessionMessage,\r\n    UpdateStatusMessage,\r\n)\r\nfrom app.utils.log import logger\r\n\r\n\r\nclass DatabaseMessageProducer:\r\n    \"\"\"数据库操作消息生产者\"\"\"\r\n\r\n    def __init__(self):\r\n        self.connection: pika.BlockingConnection | None = None\r\n        self.channel: BlockingChannel | None = None\r\n        self._initialized = False\r\n\r\n    def init_producer(self) -> bool:\r\n        \"\"\"初始化生产者\"\"\"\r\n        try:\r\n            if self._initialized and self.connection and not self.connection.is_closed:\r\n                return True\r\n\r\n            # 创建连接参数\r\n            credentials = pika.PlainCredentials(settings.rabbitmq_username, settings.rabbitmq_password)\r\n            parameters = pika.ConnectionParameters(\r\n                host=settings.rabbitmq_host,\r\n                port=settings.rabbitmq_port,\r\n                virtual_host=settings.rabbitmq_vhost,\r\n                credentials=credentials,\r\n                heartbeat=settings.rabbitmq_heartbeat,\r\n                connection_attempts=3,\r\n                retry_delay=2,\r\n            )\r\n\r\n            # 建立连接\r\n            self.connection = pika.BlockingConnection(parameters)\r\n            self.channel = self.connection.channel()\r\n\r\n            # 声明交换机和队列\r\n            assert self.channel is not None  # 类型检查断言\r\n            self.channel.exchange_declare(\r\n                exchange=settings.rabbitmq_db_exchange, exchange_type=\"direct\", durable=settings.rabbitmq_durable\r\n            )\r\n\r\n            self.channel.queue_declare(queue=settings.rabbitmq_db_queue, durable=settings.rabbitmq_durable)\r\n\r\n            # 绑定队列到交换机\r\n            self.channel.queue_bind(\r\n                exchange=settings.rabbitmq_db_exchange,\r\n                queue=settings.rabbitmq_db_queue,\r\n                routing_key=settings.rabbitmq_routing_key,\r\n            )\r\n\r\n            self._initialized = True\r\n            logger.info(f\"RabbitMQ生产者初始化成功: {settings.rabbitmq_host}:{settings.rabbitmq_port}\")\r\n            return True\r\n\r\n        except AMQPConnectionError as e:\r\n            logger.error(f\"RabbitMQ连接失败: {str(e)}\")\r\n            return False\r\n        except Exception as e:\r\n            logger.error(f\"RabbitMQ生产者初始化失败: {str(e)}\")\r\n            return False\r\n\r\n    def shutdown(self):\r\n        \"\"\"关闭生产者\"\"\"\r\n        try:\r\n            if self.channel and not self.channel.is_closed:\r\n                self.channel.close()\r\n                self.channel = None\r\n\r\n            if self.connection and not self.connection.is_closed:\r\n                self.connection.close()\r\n                self.connection = None\r\n\r\n            self._initialized = False\r\n            logger.info(\"RabbitMQ生产者已关闭\")\r\n        except Exception as e:\r\n            logger.error(f\"关闭RabbitMQ生产者失败: {str(e)}\")\r\n\r\n    def _send_message(self, message: DatabaseMessage) -> MessageResponse:\r\n        \"\"\"发送消息的通用方法\"\"\"\r\n        if not self.init_producer():\r\n            return MessageResponse(success=False, error=\"生产者初始化失败\")\r\n\r\n        try:\r\n            # 序列化消息\r\n            message_body = message.model_dump_json()\r\n\r\n            # 发送消息\r\n            assert self.channel is not None  # 类型检查断言\r\n            self.channel.basic_publish(\r\n                exchange=settings.rabbitmq_db_exchange,\r\n                routing_key=settings.rabbitmq_routing_key,\r\n                body=message_body.encode(\"utf-8\"),\r\n                properties=pika.BasicProperties(\r\n                    delivery_mode=2 if settings.rabbitmq_durable else 1,  # 消息持久化\r\n                    headers={\r\n                        \"operation_type\": message.operation_type.value,\r\n                        \"ticket_id\": message.ticket_id,\r\n                        \"message_id\": message.message_id,\r\n                        \"retry_count\": message.retry_count,\r\n                    },\r\n                ),\r\n            )\r\n\r\n            logger.debug(f\"消息发送成功: {message.message_id}, 操作: {message.operation_type.value}\")\r\n\r\n            return MessageResponse(success=True, message_id=message.message_id)\r\n\r\n        except AMQPChannelError as e:\r\n            logger.error(f\"RabbitMQ通道错误: {message.message_id}, 错误: {str(e)}\")\r\n            self._initialized = False  # 重置状态，下次调用时重新初始化\r\n            return MessageResponse(success=False, error=str(e))\r\n        except Exception as e:\r\n            logger.error(f\"发送消息失败: {message.message_id}, 错误: {str(e)}\")\r\n            return MessageResponse(success=False, error=str(e))\r\n\r\n    def send_update_status(\r\n        self, ticket_id: str, status: str, result_or_error: Any | None = None, step_log_message: str | None = None\r\n    ) -> MessageResponse:\r\n        \"\"\"发送更新任务状态消息\"\"\"\r\n        message_id = str(uuid.uuid4())\r\n\r\n        data = UpdateStatusMessage(\r\n            status=status, result_or_error=result_or_error, step_log_message=step_log_message\r\n        ).model_dump()\r\n\r\n        message = DatabaseMessage(\r\n            message_id=message_id,\r\n            operation_type=DatabaseOperationType.UPDATE_STATUS,\r\n            ticket_id=ticket_id,\r\n            data=data,\r\n            max_retry=settings.rabbitmq_max_retry_times,\r\n        )\r\n\r\n        return self._send_message(message)\r\n\r\n    def send_selenium_session(self, ticket_id: str, session_id: str) -> MessageResponse:\r\n        \"\"\"发送Selenium会话消息\"\"\"\r\n        message_id = str(uuid.uuid4())\r\n\r\n        data = UpdateSeleniumSessionMessage(session_id=session_id).model_dump()\r\n\r\n        message = DatabaseMessage(\r\n            message_id=message_id,\r\n            operation_type=DatabaseOperationType.UPDATE_SELENIUM_SESSION,\r\n            ticket_id=ticket_id,\r\n            data=data,\r\n            max_retry=settings.rabbitmq_max_retry_times,\r\n        )\r\n\r\n        return self._send_message(message)\r\n\r\n    def send_append_log(self, ticket_id: str, message_text: str, append: bool = True) -> MessageResponse:\r\n        \"\"\"发送追加日志消息\"\"\"\r\n        message_id = str(uuid.uuid4())\r\n\r\n        data = AppendLogMessage(message=message_text, append=append).model_dump()\r\n\r\n        message = DatabaseMessage(\r\n            message_id=message_id,\r\n            operation_type=DatabaseOperationType.APPEND_LOG,\r\n            ticket_id=ticket_id,\r\n            data=data,\r\n            max_retry=settings.rabbitmq_max_retry_times,\r\n        )\r\n\r\n        return self._send_message(message)\r\n\r\n    def send_update_result(self, ticket_id: str, result: Any) -> MessageResponse:\r\n        \"\"\"发送更新结果消息\"\"\"\r\n        message_id = str(uuid.uuid4())\r\n\r\n        data = UpdateResultMessage(result=result).model_dump()\r\n\r\n        message = DatabaseMessage(\r\n            message_id=message_id,\r\n            operation_type=DatabaseOperationType.UPDATE_RESULT,\r\n            ticket_id=ticket_id,\r\n            data=data,\r\n            max_retry=settings.rabbitmq_max_retry_times,\r\n        )\r\n\r\n        return self._send_message(message)\r\n\r\n    def send_update_error(self, ticket_id: str, error_message: str) -> MessageResponse:\r\n        \"\"\"发送更新错误消息\"\"\"\r\n        message_id = str(uuid.uuid4())\r\n\r\n        data = UpdateErrorMessage(error_message=error_message).model_dump()\r\n\r\n        message = DatabaseMessage(\r\n            message_id=message_id,\r\n            operation_type=DatabaseOperationType.UPDATE_ERROR,\r\n            ticket_id=ticket_id,\r\n            data=data,\r\n            max_retry=settings.rabbitmq_max_retry_times,\r\n        )\r\n\r\n        return self._send_message(message)\r\n\r\n    def send_update_file_paths(self, ticket_id: str, file_paths_data: dict) -> MessageResponse:\r\n        \"\"\"发送更新文件路径消息\"\"\"\r\n        message_id = str(uuid.uuid4())\r\n\r\n        data = UpdateFilePathsMessage(**file_paths_data).model_dump()\r\n\r\n        message = DatabaseMessage(\r\n            message_id=message_id,\r\n            operation_type=DatabaseOperationType.UPDATE_FILE_PATHS,\r\n            ticket_id=ticket_id,\r\n            data=data,\r\n            max_retry=settings.rabbitmq_max_retry_times,\r\n        )\r\n\r\n        return self._send_message(message)\r\n\r\n    def send_append_screenshot_path(self, ticket_id: str, screenshot_path: str) -> MessageResponse:\r\n        \"\"\"发送追加截图路径消息\"\"\"\r\n        message_id = str(uuid.uuid4())\r\n\r\n        data = AppendScreenshotPathMessage(screenshot_path=screenshot_path).model_dump()\r\n\r\n        message = DatabaseMessage(\r\n            message_id=message_id,\r\n            operation_type=DatabaseOperationType.APPEND_SCREENSHOT_PATH,\r\n            ticket_id=ticket_id,\r\n            data=data,\r\n            max_retry=settings.rabbitmq_max_retry_times,\r\n        )\r\n\r\n        return self._send_message(message)\r\n\r\n\r\n# 全局生产者实例（用于子进程）\r\n_global_producer: DatabaseMessageProducer | None = None\r\n\r\n\r\ndef get_producer() -> DatabaseMessageProducer:\r\n    \"\"\"获取全局生产者实例\"\"\"\r\n    global _global_producer\r\n    if _global_producer is None:\r\n        _global_producer = DatabaseMessageProducer()\r\n    return _global_producer\r\n\r\n\r\ndef cleanup_producer():\r\n    \"\"\"清理全局生产者实例\"\"\"\r\n    global _global_producer\r\n    if _global_producer:\r\n        _global_producer.shutdown()\r\n        _global_producer = None\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/services/mq_producer.py b/app/services/mq_producer.py
--- a/app/services/mq_producer.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/services/mq_producer.py	(date 1754012082287)
@@ -11,6 +11,7 @@
 from pika.adapters.blocking_connection import BlockingChannel
 from pika.exceptions import AMQPChannelError, AMQPConnectionError
 
+from app.config.enums import ExecutionStatus
 from app.config.settings import settings
 from app.schemas.mq_message import (
     AppendLogMessage,
@@ -138,7 +139,11 @@
             return MessageResponse(success=False, error=str(e))
 
     def send_update_status(
-        self, ticket_id: str, status: str, result_or_error: Any | None = None, step_log_message: str | None = None
+        self,
+        ticket_id: str,
+        status: ExecutionStatus,
+        result_or_error: Any | None = None,
+        step_log_message: str | None = None,
     ) -> MessageResponse:
         """发送更新任务状态消息"""
         message_id = str(uuid.uuid4())
Index: app/services/mq_consumer.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\nRabbitMQ消息消费者服务\r\n\r\n重构版本：采用同步数据库操作 + 线程池的架构，避免复杂的asyncio+threading组合\r\n\"\"\"\r\n\r\nimport json\r\nimport threading\r\nimport time\r\nfrom concurrent.futures import ThreadPoolExecutor\r\nfrom concurrent.futures import TimeoutError as FutureTimeoutError\r\nfrom datetime import datetime, timedelta\r\nfrom typing import Any\r\n\r\nimport pika\r\nimport psycopg2\r\nfrom pika.adapters.blocking_connection import BlockingChannel\r\nfrom pika.exceptions import AMQPChannelError, AMQPConnectionError\r\nfrom psycopg2.extras import RealDictCursor\r\n\r\nfrom app.config.settings import settings\r\nfrom app.schemas.mq_message import (\r\n    AppendLogMessage,\r\n    DatabaseMessage,\r\n    DatabaseOperationType,\r\n    UpdateErrorMessage,\r\n    UpdateResultMessage,\r\n    UpdateSeleniumSessionMessage,\r\n    UpdateStatusMessage,\r\n)\r\nfrom app.services.callback_service import get_callback_service\r\nfrom app.utils.log import logger\r\nfrom app.utils.timezone import get_shanghai_now\r\n\r\n\r\nclass DatabaseOperator:\r\n    \"\"\"同步数据库操作类，专门用于消费者线程\"\"\"\r\n\r\n    def __init__(self):\r\n        self.connection: psycopg2.extensions.connection | None = None\r\n        self._lock = threading.Lock()\r\n\r\n    def connect(self) -> bool:\r\n        \"\"\"建立数据库连接\"\"\"\r\n        try:\r\n            with self._lock:\r\n                if self.connection and not self.connection.closed:\r\n                    return True\r\n\r\n                # 解析数据库URL\r\n                db_url = settings.database_url\r\n                if not db_url or db_url == \"\":\r\n                    logger.error(\"数据库URL未配置\")\r\n                    return False\r\n\r\n                # 解析数据库URL格式：postgres://user:password@host:port/database\r\n                if db_url.startswith(\"postgres://\"):\r\n                    # 去掉协议前缀\r\n                    url_part = db_url[11:]  # 去掉 postgres://\r\n\r\n                    if \"@\" in url_part:\r\n                        auth_part, host_part = url_part.split(\"@\", 1)\r\n                        if \":\" in auth_part:\r\n                            username, password = auth_part.split(\":\", 1)\r\n                        else:\r\n                            username = auth_part\r\n                            password = \"\"\r\n\r\n                        if \"/\" in host_part:\r\n                            host_port, database = host_part.split(\"/\", 1)\r\n                        else:\r\n                            host_port = host_part\r\n                            database = \"postgres\"\r\n\r\n                        if \":\" in host_port:\r\n                            host, port = host_port.split(\":\", 1)\r\n                            port = int(port)\r\n                        else:\r\n                            host = host_port\r\n                            port = 5432  # PostgreSQL默认端口\r\n                    else:\r\n                        logger.error(f\"无效的数据库URL格式: {db_url}\")\r\n                        return False\r\n                else:\r\n                    logger.error(f\"不支持的数据库URL格式，请使用postgres://格式: {db_url}\")\r\n                    return False\r\n\r\n                self.connection = psycopg2.connect(\r\n                    host=host,\r\n                    port=port,\r\n                    user=username,\r\n                    password=password,\r\n                    database=database,\r\n                    cursor_factory=RealDictCursor,\r\n                    connect_timeout=10,\r\n                )\r\n\r\n                # PostgreSQL需要单独设置autocommit\r\n                self.connection.autocommit = True\r\n\r\n                logger.info(f\"数据库连接成功: {host}:{port}/{database}\")\r\n                return True\r\n\r\n        except Exception as e:\r\n            logger.error(f\"数据库连接失败: {str(e)}\")\r\n            return False\r\n\r\n    def close(self):\r\n        \"\"\"关闭数据库连接\"\"\"\r\n        try:\r\n            with self._lock:\r\n                if self.connection:\r\n                    self.connection.close()\r\n                    self.connection = None\r\n                    logger.debug(\"数据库连接已关闭\")\r\n        except Exception as e:\r\n            logger.debug(f\"关闭数据库连接时发生错误: {str(e)}\")\r\n\r\n    def execute_query(self, sql: str, params: tuple | None = None, fetch: bool = False) -> Any:\r\n        \"\"\"执行SQL查询\"\"\"\r\n        try:\r\n            with self._lock:\r\n                if not self.connection or self.connection.closed:\r\n                    if not self.connect():\r\n                        raise Exception(\"无法建立数据库连接\")\r\n\r\n                assert self.connection is not None  # 类型检查断言\r\n                with self.connection.cursor() as cursor:\r\n                    cursor.execute(sql, params or ())\r\n\r\n                    if fetch:\r\n                        return cursor.fetchall()\r\n                    else:\r\n                        return cursor.rowcount\r\n\r\n        except Exception as e:\r\n            logger.error(f\"数据库操作失败: SQL={sql}, 错误={str(e)}\")\r\n            # 连接可能已断开，下次重新连接\r\n            self.connection = None\r\n            raise\r\n\r\n    def update_task_status(self, ticket_id: str, status: str, **kwargs) -> bool:\r\n        \"\"\"更新任务状态\"\"\"\r\n        try:\r\n            set_clauses = [\"status = %s\", \"updated_at = %s\"]\r\n            params = [status, get_shanghai_now()]\r\n\r\n            # 处理额外的字段更新，确保序列化字典类型\r\n            for field, value in kwargs.items():\r\n                if field in [\"result\", \"error_message\", \"step_log\", \"replica_id\", \"started_at\", \"completed_at\"]:\r\n                    set_clauses.append(f\"{field} = %s\")\r\n\r\n                    # 如果是字典或列表，转换为JSON字符串\r\n                    if isinstance(value, dict | list):\r\n                        value = json.dumps(value, ensure_ascii=False)\r\n\r\n                    params.append(value)\r\n\r\n            sql = f\"UPDATE task SET {', '.join(set_clauses)} WHERE ticket_id = %s\"\r\n            params.append(ticket_id)\r\n\r\n            # 调试日志\r\n            logger.debug(f\"SQL: {sql}\")\r\n            logger.debug(f\"参数: {params}\")\r\n\r\n            rowcount = self.execute_query(sql, tuple(params))\r\n\r\n            if rowcount == 0:\r\n                logger.warning(f\"任务不存在或已被删除: {ticket_id}\")\r\n                return True  # 认为成功，避免重试\r\n            else:\r\n                logger.info(f\"更新任务状态成功: {ticket_id} -> {status}\")\r\n                return True\r\n\r\n        except Exception as e:\r\n            logger.error(f\"更新任务状态失败: {ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def update_selenium_session(self, ticket_id: str, session_id: str) -> bool:\r\n        \"\"\"更新Selenium会话\"\"\"\r\n        try:\r\n            sql = \"UPDATE task SET selenium_session_id = %s, updated_at = %s WHERE ticket_id = %s\"\r\n            self.execute_query(sql, (session_id, get_shanghai_now(), ticket_id))\r\n            return True\r\n        except Exception as e:\r\n            logger.error(f\"更新Selenium会话失败: {ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def append_task_log(self, ticket_id: str, message: str, append: bool = True) -> bool:\r\n        \"\"\"追加任务日志\"\"\"\r\n        try:\r\n            timestamp = get_shanghai_now().strftime(\"%Y-%m-%d %H:%M:%S\")\r\n            formatted_message = f\"{timestamp}：{message}\"\r\n\r\n            if append:\r\n                # 查询当前日志\r\n                current_log_sql = \"SELECT step_log FROM task WHERE ticket_id = %s\"\r\n                result = self.execute_query(current_log_sql, (ticket_id,), fetch=True)\r\n\r\n                if result:\r\n                    current_log = result[0].get(\"step_log\", \"\") or \"\"\r\n                    updated_log = f\"{current_log}\\n{formatted_message}\" if current_log else formatted_message\r\n                else:\r\n                    updated_log = formatted_message\r\n            else:\r\n                updated_log = formatted_message\r\n\r\n            # 更新日志\r\n            update_sql = \"UPDATE task SET step_log = %s, updated_at = %s WHERE ticket_id = %s\"\r\n            self.execute_query(update_sql, (updated_log, get_shanghai_now(), ticket_id))\r\n\r\n            logger.debug(f\"更新步骤日志成功: {ticket_id}\")\r\n            return True\r\n\r\n        except Exception as e:\r\n            logger.error(f\"更新步骤日志失败: {ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def update_task_result(self, ticket_id: str, result: Any) -> bool:\r\n        \"\"\"更新任务结果\"\"\"\r\n        try:\r\n            # 如果结果是字典或列表，转换为JSON字符串\r\n            if isinstance(result, dict | list):\r\n                result = json.dumps(result, ensure_ascii=False)\r\n\r\n            sql = \"UPDATE task SET result = %s, updated_at = %s WHERE ticket_id = %s\"\r\n            self.execute_query(sql, (result, get_shanghai_now(), ticket_id))\r\n\r\n            logger.debug(f\"更新任务结果成功: {ticket_id}\")\r\n            return True\r\n\r\n        except Exception as e:\r\n            logger.error(f\"更新任务结果失败: {ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def update_task_error(self, ticket_id: str, error_message: str) -> bool:\r\n        \"\"\"更新任务错误\"\"\"\r\n        try:\r\n            sql = \"UPDATE task SET error_message = %s, status = %s, updated_at = %s WHERE ticket_id = %s\"\r\n            self.execute_query(sql, (error_message, \"FAILURE\", get_shanghai_now(), ticket_id))\r\n\r\n            logger.debug(f\"更新任务错误成功: {ticket_id}\")\r\n            return True\r\n\r\n        except Exception as e:\r\n            logger.error(f\"更新任务错误失败: {ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def update_task_file_paths(\r\n        self,\r\n        ticket_id: str,\r\n        video_file_paths: list[str] | None,\r\n        screenshot_file_paths: list[str] | None,\r\n        download_file_paths: list[str] | None,\r\n    ) -> bool:\r\n        \"\"\"更新任务文件路径\"\"\"\r\n        try:\r\n            import json\r\n\r\n            # 将列表转换为JSON字符串\r\n            video_paths_json = json.dumps(video_file_paths) if video_file_paths else None\r\n            screenshot_paths_json = json.dumps(screenshot_file_paths) if screenshot_file_paths else None\r\n            download_paths_json = json.dumps(download_file_paths) if download_file_paths else None\r\n\r\n            sql = \"\"\"\r\n                UPDATE task SET\r\n                    video_file_paths = %s,\r\n                    screenshot_file_paths = %s,\r\n                    download_file_paths = %s,\r\n                    updated_at = %s\r\n                WHERE ticket_id = %s\r\n            \"\"\"\r\n\r\n            self.execute_query(\r\n                sql,\r\n                (\r\n                    video_paths_json,\r\n                    screenshot_paths_json,\r\n                    download_paths_json,\r\n                    get_shanghai_now(),\r\n                    ticket_id,\r\n                ),\r\n            )\r\n\r\n            logger.debug(f\"更新任务文件路径成功: {ticket_id}\")\r\n            return True\r\n\r\n        except Exception as e:\r\n            logger.error(f\"更新任务文件路径失败: {ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def append_screenshot_path(self, ticket_id: str, screenshot_path: str) -> bool:\r\n        \"\"\"追加截图路径到数据库（原子性操作）\"\"\"\r\n        try:\r\n            # 使用PostgreSQL的JSON操作来原子性地追加截图路径\r\n            # 如果screenshot_file_paths为NULL，则创建新数组；否则追加到现有数组\r\n            sql = \"\"\"\r\n                UPDATE task SET\r\n                    screenshot_file_paths = CASE\r\n                        WHEN screenshot_file_paths IS NULL THEN %s::jsonb\r\n                        ELSE screenshot_file_paths || %s::jsonb\r\n                    END,\r\n                    updated_at = %s\r\n                WHERE ticket_id = %s\r\n            \"\"\"\r\n\r\n            import json\r\n\r\n            # 创建包含新截图路径的JSON数组\r\n            new_path_json = json.dumps([screenshot_path])\r\n\r\n            self.execute_query(\r\n                sql,\r\n                (\r\n                    new_path_json,  # 用于NULL情况的新数组\r\n                    new_path_json,  # 用于追加的数组\r\n                    get_shanghai_now(),\r\n                    ticket_id,\r\n                ),\r\n            )\r\n\r\n            logger.debug(f\"追加截图路径成功: {ticket_id} -> {screenshot_path}\")\r\n            return True\r\n\r\n        except Exception as e:\r\n            logger.error(f\"追加截图路径失败: {ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n\r\nclass DatabaseMessageConsumer:\r\n    \"\"\"数据库操作消息消费者 - 重构版本\"\"\"\r\n\r\n    def __init__(self):\r\n        self.connection: pika.BlockingConnection | None = None\r\n        self.channel: BlockingChannel | None = None\r\n        self._running = False\r\n        self._consumer_thread: threading.Thread | None = None\r\n        self._processed_messages: set[str] = set()  # 幂等性控制\r\n        self._message_timestamps: dict[str, datetime] = {}  # 消息时间戳\r\n        self._executor: ThreadPoolExecutor | None = None\r\n        self._db_operator: DatabaseOperator | None = None\r\n\r\n        # 统计信息\r\n        self._stats = {\r\n            \"total_messages\": 0,\r\n            \"successful_messages\": 0,\r\n            \"failed_messages\": 0,\r\n            \"timeout_messages\": 0,\r\n            \"dead_letter_messages\": 0,\r\n            \"retry_messages\": 0,\r\n            \"processing_times\": [],  # 存储最近100个处理时间\r\n        }\r\n        self._stats_lock = threading.Lock()\r\n\r\n    def start_consumer(self):\r\n        \"\"\"启动消费者\"\"\"\r\n        if self._running:\r\n            logger.warning(\"消费者已经在运行中\")\r\n            return\r\n\r\n        try:\r\n            logger.info(\"\uD83D\uDE80 正在启动RabbitMQ消费者...\")\r\n\r\n            # 创建线程池和数据库操作器\r\n            logger.info(\"\uD83D\uDCE6 创建线程池和数据库操作器...\")\r\n            self._executor = ThreadPoolExecutor(max_workers=8, thread_name_prefix=\"db_worker\")\r\n            self._db_operator = DatabaseOperator()\r\n\r\n            # 启动消费者线程\r\n            logger.info(\"\uD83D\uDD04 启动消费者线程...\")\r\n            self._consumer_thread = threading.Thread(\r\n                target=self._consumer_main_loop, daemon=True, name=\"rabbitmq_consumer\"\r\n            )\r\n            self._consumer_thread.start()\r\n\r\n            self._running = True\r\n            logger.info(f\"✅ RabbitMQ消费者启动成功: {settings.rabbitmq_host}:{settings.rabbitmq_port}\")\r\n\r\n        except Exception as e:\r\n            import traceback\r\n\r\n            logger.error(f\"❌ 启动RabbitMQ消费者失败: {str(e)}\")\r\n            logger.error(f\"错误堆栈: {traceback.format_exc()}\")\r\n            self._cleanup()\r\n            raise\r\n\r\n    def _consumer_main_loop(self):\r\n        \"\"\"消费者主循环\"\"\"\r\n        retry_count = 0\r\n        max_retries = 3\r\n\r\n        logger.info(\"\uD83D\uDD04 进入消费者主循环...\")\r\n\r\n        while self._running and retry_count < max_retries:\r\n            try:\r\n                logger.info(f\"\uD83C\uDF10 尝试建立RabbitMQ连接 (尝试 {retry_count + 1}/{max_retries + 1})...\")\r\n\r\n                # 建立RabbitMQ连接\r\n                if not self._connect_rabbitmq():\r\n                    retry_count += 1\r\n                    if retry_count < max_retries:\r\n                        logger.warning(f\"⚠\uFE0F RabbitMQ连接失败，{5}秒后重试... ({retry_count}/{max_retries})\")\r\n                        time.sleep(5)\r\n                        continue\r\n                    else:\r\n                        logger.error(\"❌ RabbitMQ连接重试次数已达上限，停止消费者\")\r\n                        break\r\n\r\n                logger.info(\"✅ RabbitMQ连接成功，尝试建立数据库连接...\")\r\n\r\n                # 建立数据库连接\r\n                assert self._db_operator is not None  # 类型检查断言\r\n                if not self._db_operator.connect():\r\n                    logger.error(\"❌ 数据库连接失败，停止消费者\")\r\n                    break\r\n\r\n                retry_count = 0  # 重置重试计数\r\n                logger.info(\"✅ 数据库连接成功，开始消费消息...\")\r\n\r\n                # 启动清理任务\r\n                self._start_cleanup_task()\r\n\r\n                # 消费循环\r\n                while self._running:\r\n                    try:\r\n                        assert self.connection is not None  # 类型检查断言\r\n                        self.connection.process_data_events(time_limit=1)\r\n\r\n                        # 检查连接状态\r\n                        if self.connection.is_closed:\r\n                            logger.warning(\"⚠\uFE0F RabbitMQ连接已关闭，尝试重连...\")\r\n                            break\r\n\r\n                    except (AMQPConnectionError, AMQPChannelError) as e:\r\n                        if self._running:\r\n                            logger.error(f\"❌ RabbitMQ连接错误: {str(e)}\")\r\n                            break\r\n                    except Exception as e:\r\n                        if self._running:\r\n                            logger.error(f\"❌ 消费消息时发生错误: {str(e)}\")\r\n                            time.sleep(1)\r\n\r\n            except Exception as e:\r\n                if self._running:\r\n                    import traceback\r\n\r\n                    logger.error(f\"❌ 消费者主循环异常: {str(e)}\")\r\n                    logger.error(f\"错误堆栈: {traceback.format_exc()}\")\r\n                    retry_count += 1\r\n                    if retry_count < max_retries:\r\n                        logger.info(\"\uD83D\uDD04 5秒后重试...\")\r\n                        time.sleep(5)\r\n\r\n        logger.info(\"\uD83D\uDED1 消费者主循环结束\")\r\n        self._cleanup()\r\n\r\n    def _connect_rabbitmq(self) -> bool:\r\n        \"\"\"建立RabbitMQ连接\"\"\"\r\n        try:\r\n            # 创建连接参数\r\n            credentials = pika.PlainCredentials(settings.rabbitmq_username, settings.rabbitmq_password)\r\n            parameters = pika.ConnectionParameters(\r\n                host=settings.rabbitmq_host,\r\n                port=settings.rabbitmq_port,\r\n                virtual_host=settings.rabbitmq_vhost,\r\n                credentials=credentials,\r\n                heartbeat=settings.rabbitmq_heartbeat,\r\n                connection_attempts=1,\r\n                retry_delay=2,\r\n            )\r\n\r\n            # 建立连接\r\n            self.connection = pika.BlockingConnection(parameters)\r\n            self.channel = self.connection.channel()\r\n\r\n            # 声明死信交换机和队列\r\n            self.channel.exchange_declare(\r\n                exchange=settings.rabbitmq_dead_letter_exchange,\r\n                exchange_type=\"direct\",\r\n                durable=settings.rabbitmq_durable,\r\n            )\r\n\r\n            self.channel.queue_declare(queue=settings.rabbitmq_dead_letter_queue, durable=settings.rabbitmq_durable)\r\n\r\n            self.channel.queue_bind(\r\n                exchange=settings.rabbitmq_dead_letter_exchange,\r\n                queue=settings.rabbitmq_dead_letter_queue,\r\n                routing_key=settings.rabbitmq_dead_letter_routing_key,\r\n            )\r\n\r\n            # 声明主交换机和队列\r\n            self.channel.exchange_declare(\r\n                exchange=settings.rabbitmq_db_exchange, exchange_type=\"direct\", durable=settings.rabbitmq_durable\r\n            )\r\n\r\n            self.channel.queue_declare(queue=settings.rabbitmq_db_queue, durable=settings.rabbitmq_durable)\r\n\r\n            # 绑定队列到交换机\r\n            self.channel.queue_bind(\r\n                exchange=settings.rabbitmq_db_exchange,\r\n                queue=settings.rabbitmq_db_queue,\r\n                routing_key=settings.rabbitmq_routing_key,\r\n            )\r\n\r\n            # 设置消费者预取数量和回调\r\n            self.channel.basic_qos(prefetch_count=1)\r\n            self.channel.basic_consume(\r\n                queue=settings.rabbitmq_db_queue, on_message_callback=self._message_callback, auto_ack=False\r\n            )\r\n\r\n            logger.debug(\"RabbitMQ连接和配置完成\")\r\n            return True\r\n\r\n        except Exception as e:\r\n            logger.error(f\"建立RabbitMQ连接失败: {str(e)}\")\r\n            return False\r\n\r\n    def _message_callback(self, channel, method, properties, body):\r\n        \"\"\"消息回调处理函数\"\"\"\r\n        if not self._running:\r\n            logger.debug(\"消费者已停止，忽略消息\")\r\n            return\r\n\r\n        try:\r\n            logger.info(\"\uD83D\uDCE8 收到新消息，开始处理...\")\r\n\r\n            # 解析消息\r\n            message_body = body.decode(\"utf-8\")\r\n            message_data = json.loads(message_body)\r\n            db_message = DatabaseMessage(**message_data)\r\n\r\n            logger.info(\r\n                f\"\uD83D\uDCCB 消息详情: ID={db_message.message_id}, 操作={db_message.operation_type.value}, 任务={db_message.ticket_id}, 重试次数={db_message.retry_count}/{db_message.max_retry}\"\r\n            )\r\n\r\n            # 幂等性检查\r\n            if db_message.message_id in self._processed_messages:\r\n                logger.debug(f\"♻\uFE0F 消息已处理，跳过: {db_message.message_id}\")\r\n                channel.basic_ack(delivery_tag=method.delivery_tag)\r\n                return\r\n\r\n            # 记录消息时间戳和统计\r\n            start_time = datetime.now()\r\n            self._message_timestamps[db_message.message_id] = start_time\r\n\r\n            with self._stats_lock:\r\n                self._stats[\"total_messages\"] += 1\r\n\r\n            # 提交到线程池处理\r\n            try:\r\n                logger.info(f\"⚡ 提交消息到线程池处理: {db_message.message_id}\")\r\n                future = self._executor.submit(self._process_message, db_message)\r\n                success = future.result(timeout=settings.rabbitmq_message_timeout)\r\n\r\n                # 计算处理时间\r\n                processing_time = (datetime.now() - start_time).total_seconds()\r\n\r\n                if success:\r\n                    # 标记消息已处理\r\n                    self._processed_messages.add(db_message.message_id)\r\n                    channel.basic_ack(delivery_tag=method.delivery_tag)\r\n                    logger.info(f\"✅ 消息处理成功: {db_message.message_id} (耗时: {processing_time:.2f}秒)\")\r\n\r\n                    # 更新统计\r\n                    with self._stats_lock:\r\n                        self._stats[\"successful_messages\"] += 1\r\n                        self._stats[\"processing_times\"].append(processing_time)\r\n                        # 只保留最近100个处理时间\r\n                        if len(self._stats[\"processing_times\"]) > 100:\r\n                            self._stats[\"processing_times\"].pop(0)\r\n                else:\r\n                    # 处理失败，检查重试次数\r\n                    with self._stats_lock:\r\n                        self._stats[\"failed_messages\"] += 1\r\n                    self._handle_message_failure(channel, method, db_message, \"处理失败\")\r\n\r\n            except FutureTimeoutError:\r\n                logger.error(\r\n                    f\"⏰ 消息处理超时: {db_message.message_id} (超时时间: {settings.rabbitmq_message_timeout}秒)\"\r\n                )\r\n                with self._stats_lock:\r\n                    self._stats[\"timeout_messages\"] += 1\r\n                self._handle_message_failure(channel, method, db_message, \"处理超时\")\r\n            except Exception as e:\r\n                logger.error(f\"❌ 消息处理异常: {db_message.message_id}, 错误: {str(e)}\")\r\n                with self._stats_lock:\r\n                    self._stats[\"failed_messages\"] += 1\r\n                self._handle_message_failure(channel, method, db_message, f\"处理异常: {str(e)}\")\r\n\r\n        except Exception as e:\r\n            import traceback\r\n\r\n            logger.error(f\"❌ 消息回调异常: {str(e)}\")\r\n            logger.error(f\"错误堆栈: {traceback.format_exc()}\")\r\n            try:\r\n                channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)\r\n            except Exception as nack_error:\r\n                logger.error(f\"❌ 消息拒绝失败: {str(nack_error)}\")\r\n\r\n    def _handle_message_failure(self, channel, method, db_message: DatabaseMessage, failure_reason: str):\r\n        \"\"\"处理消息失败的情况，实现重试机制和死信队列\"\"\"\r\n        try:\r\n            # 递增重试次数\r\n            db_message.retry_count += 1\r\n\r\n            logger.warning(\r\n                f\"\uD83D\uDD04 消息处理失败: {db_message.message_id}, 原因: {failure_reason}, 重试次数: {db_message.retry_count}/{db_message.max_retry}\"\r\n            )\r\n\r\n            # 检查是否超过最大重试次数\r\n            if db_message.retry_count >= db_message.max_retry:\r\n                # 发送到死信队列\r\n                logger.error(f\"\uD83D\uDC80 消息超过最大重试次数，发送到死信队列: {db_message.message_id}\")\r\n                self._send_to_dead_letter_queue(db_message, failure_reason)\r\n\r\n                # 确认消息（不再重试）\r\n                channel.basic_ack(delivery_tag=method.delivery_tag)\r\n\r\n                # 标记为已处理（避免重复处理）\r\n                self._processed_messages.add(db_message.message_id)\r\n\r\n                # 更新统计\r\n                with self._stats_lock:\r\n                    self._stats[\"dead_letter_messages\"] += 1\r\n            else:\r\n                # 重新发布消息到队列（带有更新的重试次数）\r\n                logger.info(f\"\uD83D\uDD01 重新发布消息到队列: {db_message.message_id}, 重试次数: {db_message.retry_count}\")\r\n                self._republish_message_with_retry(db_message)\r\n\r\n                # 确认原消息\r\n                channel.basic_ack(delivery_tag=method.delivery_tag)\r\n\r\n                # 更新统计\r\n                with self._stats_lock:\r\n                    self._stats[\"retry_messages\"] += 1\r\n\r\n        except Exception as e:\r\n            logger.error(f\"❌ 处理消息失败时发生异常: {db_message.message_id}, 错误: {str(e)}\")\r\n            # 如果处理失败逻辑本身出错，则简单重新排队\r\n            try:\r\n                channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)\r\n            except Exception as nack_error:\r\n                logger.error(f\"❌ 消息拒绝失败: {str(nack_error)}\")\r\n\r\n    def _send_to_dead_letter_queue(self, db_message: DatabaseMessage, failure_reason: str):\r\n        \"\"\"发送消息到死信队列\"\"\"\r\n        try:\r\n            # 添加失败信息到消息数据\r\n            dead_letter_data = db_message.model_dump()\r\n            dead_letter_data[\"failure_reason\"] = failure_reason\r\n            dead_letter_data[\"failed_at\"] = get_shanghai_now().isoformat()\r\n\r\n            # 发布到死信队列\r\n            if self.channel:\r\n                self.channel.basic_publish(\r\n                    exchange=settings.rabbitmq_dead_letter_exchange,\r\n                    routing_key=settings.rabbitmq_dead_letter_routing_key,\r\n                    body=json.dumps(dead_letter_data, ensure_ascii=False),\r\n                    properties=pika.BasicProperties(\r\n                        delivery_mode=2 if settings.rabbitmq_durable else 1,  # 持久化\r\n                        timestamp=int(datetime.now().timestamp()),\r\n                    ),\r\n                )\r\n                logger.info(f\"\uD83D\uDC80 消息已发送到死信队列: {db_message.message_id}\")\r\n            else:\r\n                logger.error(f\"❌ 无法发送到死信队列，channel为空: {db_message.message_id}\")\r\n\r\n        except Exception as e:\r\n            logger.error(f\"❌ 发送消息到死信队列失败: {db_message.message_id}, 错误: {str(e)}\")\r\n\r\n    def _republish_message_with_retry(self, db_message: DatabaseMessage):\r\n        \"\"\"重新发布消息到队列（带有更新的重试次数）\"\"\"\r\n        try:\r\n            # 更新消息的时间戳\r\n            db_message.created_at = get_shanghai_now()\r\n\r\n            # 发布到主队列\r\n            if self.channel:\r\n                self.channel.basic_publish(\r\n                    exchange=settings.rabbitmq_db_exchange,\r\n                    routing_key=settings.rabbitmq_routing_key,\r\n                    body=json.dumps(db_message.model_dump(), ensure_ascii=False),\r\n                    properties=pika.BasicProperties(\r\n                        delivery_mode=2 if settings.rabbitmq_durable else 1,  # 持久化\r\n                        timestamp=int(datetime.now().timestamp()),\r\n                    ),\r\n                )\r\n                logger.debug(f\"\uD83D\uDD01 消息已重新发布: {db_message.message_id}\")\r\n            else:\r\n                logger.error(f\"❌ 无法重新发布消息，channel为空: {db_message.message_id}\")\r\n\r\n        except Exception as e:\r\n            logger.error(f\"❌ 重新发布消息失败: {db_message.message_id}, 错误: {str(e)}\")\r\n\r\n    def _process_message(self, message: DatabaseMessage) -> bool:\r\n        \"\"\"处理具体的数据库操作消息（在线程池中执行）\"\"\"\r\n        try:\r\n            logger.debug(\r\n                f\"处理消息: {message.message_id}, 操作: {message.operation_type.value}, 任务: {message.ticket_id}\"\r\n            )\r\n\r\n            # 根据操作类型分发处理\r\n            if message.operation_type == DatabaseOperationType.UPDATE_STATUS:\r\n                return self._handle_update_status(message)\r\n            elif message.operation_type == DatabaseOperationType.UPDATE_SELENIUM_SESSION:\r\n                return self._handle_update_selenium_session(message)\r\n            elif message.operation_type == DatabaseOperationType.APPEND_LOG:\r\n                return self._handle_append_log(message)\r\n            elif message.operation_type == DatabaseOperationType.UPDATE_RESULT:\r\n                return self._handle_update_result(message)\r\n            elif message.operation_type == DatabaseOperationType.UPDATE_ERROR:\r\n                return self._handle_update_error(message)\r\n            elif message.operation_type == DatabaseOperationType.UPDATE_FILE_PATHS:\r\n                return self._handle_update_file_paths(message)\r\n            elif message.operation_type == DatabaseOperationType.APPEND_SCREENSHOT_PATH:\r\n                return self._handle_append_screenshot_path(message)\r\n            else:\r\n                logger.error(f\"未知的操作类型: {message.operation_type}\")\r\n                return False\r\n\r\n        except Exception as e:\r\n            logger.error(f\"处理消息异常: {message.message_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def _handle_update_status(self, message: DatabaseMessage) -> bool:\r\n        \"\"\"处理更新状态消息\"\"\"\r\n        try:\r\n            data = UpdateStatusMessage(**message.data)\r\n            now = get_shanghai_now()\r\n            kwargs = {}\r\n            if data.status == \"SUCCESS\":\r\n                kwargs.update({\"result\": data.result_or_error, \"error_message\": None, \"completed_at\": now})\r\n            elif data.status == \"RUNNING\":\r\n                kwargs.update({\"replica_id\": settings.replica_id, \"started_at\": now})\r\n            else:  # FAILURE or CANCELLED\r\n                kwargs.update(\r\n                    {\r\n                        \"result\": None,\r\n                        \"error_message\": str(data.result_or_error) if data.result_or_error else None,\r\n                        \"completed_at\": now,\r\n                    }\r\n                )\r\n\r\n            # 如果有步骤日志消息，也一起更新\r\n            if data.step_log_message:\r\n                # 先更新日志\r\n                self._db_operator.append_task_log(message.ticket_id, data.step_log_message, append=True)\r\n\r\n            # 更新数据库状态\r\n            db_update_success = self._db_operator.update_task_status(message.ticket_id, data.status, **kwargs)\r\n\r\n            # 对于最终状态（SUCCESS, FAILURE, CANCELLED），发送回调到主服务\r\n            if data.status in [\"SUCCESS\", \"FAILURE\", \"CANCELLED\"] and db_update_success:\r\n                try:\r\n                    callback_service = get_callback_service()\r\n\r\n                    # 准备回调参数\r\n                    callback_kwargs = {\"ticket_id\": message.ticket_id, \"status\": data.status}\r\n\r\n                    if data.status == \"FAILURE\":\r\n                        callback_kwargs[\"error_message\"] = (\r\n                            str(data.result_or_error) if data.result_or_error else \"执行失败\"\r\n                        )\r\n                    elif data.status == \"CANCELLED\":\r\n                        callback_kwargs[\"error_message\"] = \"任务被取消\"\r\n\r\n                    # 发送回调（使用同步方式，因为我们在线程池中）\r\n                    callback_success = callback_service.send_task_callback_sync(**callback_kwargs)\r\n\r\n                    if callback_success:\r\n                        logger.info(f\"任务状态回调发送成功: {message.ticket_id} -> {data.status}\")\r\n                    else:\r\n                        # 回调失败不影响主流程，记录警告而不是错误\r\n                        logger.warning(f\"任务状态回调发送失败: {message.ticket_id} -> {data.status} (不影响任务执行)\")\r\n\r\n                except Exception as callback_error:\r\n                    logger.warning(\r\n                        f\"发送回调时发生异常: {message.ticket_id}, 错误: {str(callback_error)} (不影响任务执行)\"\r\n                    )\r\n                    # 不影响主流程，回调失败不应该导致消息处理失败\r\n\r\n            return db_update_success\r\n\r\n        except Exception as e:\r\n            logger.error(f\"处理更新状态消息失败: {message.ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def _handle_update_selenium_session(self, message: DatabaseMessage) -> bool:\r\n        \"\"\"处理更新Selenium会话消息\"\"\"\r\n        try:\r\n            data = UpdateSeleniumSessionMessage(**message.data)\r\n            return self._db_operator.update_selenium_session(message.ticket_id, data.session_id)\r\n        except Exception as e:\r\n            logger.error(f\"处理更新Selenium会话消息失败: {message.ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def _handle_append_log(self, message: DatabaseMessage) -> bool:\r\n        \"\"\"处理追加日志消息\"\"\"\r\n        try:\r\n            data = AppendLogMessage(**message.data)\r\n            return self._db_operator.append_task_log(message.ticket_id, data.message, data.append)\r\n        except Exception as e:\r\n            logger.error(f\"处理追加日志消息失败: {message.ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def _handle_update_result(self, message: DatabaseMessage) -> bool:\r\n        \"\"\"处理更新结果消息\"\"\"\r\n        try:\r\n            data = UpdateResultMessage(**message.data)\r\n            return self._db_operator.update_task_result(message.ticket_id, data.result)\r\n        except Exception as e:\r\n            logger.error(f\"处理更新结果消息失败: {message.ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def _handle_update_error(self, message: DatabaseMessage) -> bool:\r\n        \"\"\"处理更新错误消息\"\"\"\r\n        try:\r\n            data = UpdateErrorMessage(**message.data)\r\n            return self._db_operator.update_task_error(message.ticket_id, data.error_message)\r\n        except Exception as e:\r\n            logger.error(f\"处理更新错误消息失败: {message.ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def _handle_update_file_paths(self, message: DatabaseMessage) -> bool:\r\n        \"\"\"处理更新文件路径消息\"\"\"\r\n        try:\r\n            from app.schemas.mq_message import UpdateFilePathsMessage\r\n\r\n            data = UpdateFilePathsMessage(**message.data)\r\n            return self._db_operator.update_task_file_paths(\r\n                message.ticket_id,\r\n                data.video_file_paths,\r\n                data.screenshot_file_paths,\r\n                data.download_file_paths,\r\n            )\r\n        except Exception as e:\r\n            logger.error(f\"处理更新文件路径消息失败: {message.ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def _handle_append_screenshot_path(self, message: DatabaseMessage) -> bool:\r\n        \"\"\"处理追加截图路径消息\"\"\"\r\n        try:\r\n            from app.schemas.mq_message import AppendScreenshotPathMessage\r\n\r\n            data = AppendScreenshotPathMessage(**message.data)\r\n            return self._db_operator.append_screenshot_path(message.ticket_id, data.screenshot_path)\r\n        except Exception as e:\r\n            logger.error(f\"处理追加截图路径消息失败: {message.ticket_id}, 错误: {str(e)}\")\r\n            return False\r\n\r\n    def _start_cleanup_task(self):\r\n        \"\"\"启动清理任务\"\"\"\r\n\r\n        def cleanup_old_messages():\r\n            stats_log_counter = 0\r\n            while self._running:\r\n                try:\r\n                    time.sleep(60)  # 每分钟清理一次\r\n                    now = datetime.now()\r\n\r\n                    # 清理超过1小时的消息记录\r\n                    expired_messages = [\r\n                        msg_id\r\n                        for msg_id, timestamp in self._message_timestamps.items()\r\n                        if now - timestamp > timedelta(hours=1)\r\n                    ]\r\n\r\n                    for msg_id in expired_messages:\r\n                        self._processed_messages.discard(msg_id)\r\n                        del self._message_timestamps[msg_id]\r\n\r\n                    if expired_messages:\r\n                        logger.debug(f\"清理了 {len(expired_messages)} 条过期消息记录\")\r\n\r\n                    # 每5分钟记录一次统计信息\r\n                    stats_log_counter += 1\r\n                    if stats_log_counter >= 5:\r\n                        self.log_stats()\r\n                        stats_log_counter = 0\r\n\r\n                except Exception as e:\r\n                    logger.debug(f\"清理消息记录时发生错误: {str(e)}\")\r\n\r\n        cleanup_thread = threading.Thread(target=cleanup_old_messages, daemon=True, name=\"message_cleanup\")\r\n        cleanup_thread.start()\r\n\r\n    def get_stats(self) -> dict:\r\n        \"\"\"获取消费者统计信息\"\"\"\r\n        with self._stats_lock:\r\n            stats = self._stats.copy()\r\n\r\n            # 计算平均处理时间\r\n            if stats[\"processing_times\"]:\r\n                stats[\"avg_processing_time\"] = sum(stats[\"processing_times\"]) / len(stats[\"processing_times\"])\r\n                stats[\"max_processing_time\"] = max(stats[\"processing_times\"])\r\n                stats[\"min_processing_time\"] = min(stats[\"processing_times\"])\r\n            else:\r\n                stats[\"avg_processing_time\"] = 0\r\n                stats[\"max_processing_time\"] = 0\r\n                stats[\"min_processing_time\"] = 0\r\n\r\n            # 计算成功率\r\n            if stats[\"total_messages\"] > 0:\r\n                stats[\"success_rate\"] = stats[\"successful_messages\"] / stats[\"total_messages\"] * 100\r\n            else:\r\n                stats[\"success_rate\"] = 0\r\n\r\n            # 添加线程池状态\r\n            if self._executor:\r\n                stats[\"thread_pool_active\"] = True\r\n                # 注意：ThreadPoolExecutor没有直接的方法获取活跃线程数，这里只是示例\r\n                stats[\"thread_pool_max_workers\"] = self._executor._max_workers\r\n            else:\r\n                stats[\"thread_pool_active\"] = False\r\n                stats[\"thread_pool_max_workers\"] = 0\r\n\r\n            # 移除原始处理时间列表（太长了）\r\n            del stats[\"processing_times\"]\r\n\r\n            return stats\r\n\r\n    def log_stats(self):\r\n        \"\"\"记录统计信息到日志\"\"\"\r\n        stats = self.get_stats()\r\n        logger.info(\r\n            f\"\uD83D\uDCCA 消费者统计信息: \"\r\n            f\"总消息={stats['total_messages']}, \"\r\n            f\"成功={stats['successful_messages']}, \"\r\n            f\"失败={stats['failed_messages']}, \"\r\n            f\"超时={stats['timeout_messages']}, \"\r\n            f\"死信={stats['dead_letter_messages']}, \"\r\n            f\"重试={stats['retry_messages']}, \"\r\n            f\"成功率={stats['success_rate']:.1f}%, \"\r\n            f\"平均处理时间={stats['avg_processing_time']:.2f}秒\"\r\n        )\r\n\r\n    def stop_consumer(self):\r\n        \"\"\"停止消费者\"\"\"\r\n        try:\r\n            logger.info(\"正在停止RabbitMQ消费者...\")\r\n            self._running = False\r\n\r\n            # 等待消费者线程结束\r\n            if self._consumer_thread and self._consumer_thread.is_alive():\r\n                logger.debug(\"等待消费者线程结束...\")\r\n                self._consumer_thread.join(timeout=10)\r\n\r\n                if self._consumer_thread.is_alive():\r\n                    logger.warning(\"消费者线程未能在10秒内结束\")\r\n\r\n            self._cleanup()\r\n            logger.info(\"RabbitMQ消费者已停止\")\r\n\r\n        except Exception as e:\r\n            logger.error(f\"停止RabbitMQ消费者失败: {str(e)}\")\r\n\r\n    def _cleanup(self):\r\n        \"\"\"清理资源\"\"\"\r\n        try:\r\n            # 关闭线程池\r\n            if self._executor:\r\n                self._executor.shutdown(wait=True)\r\n                self._executor = None\r\n\r\n            # 关闭数据库连接\r\n            if self._db_operator:\r\n                self._db_operator.close()\r\n                self._db_operator = None\r\n\r\n            # 关闭RabbitMQ连接\r\n            if self.channel and not self.channel.is_closed:\r\n                self.channel.close()\r\n                self.channel = None\r\n\r\n            if self.connection and not self.connection.is_closed:\r\n                self.connection.close()\r\n                self.connection = None\r\n\r\n            logger.debug(\"资源清理完成\")\r\n\r\n        except Exception as e:\r\n            logger.debug(f\"清理资源时发生错误: {str(e)}\")\r\n\r\n\r\n# 全局消费者实例\r\n_global_consumer: DatabaseMessageConsumer | None = None\r\n\r\n\r\ndef get_consumer() -> DatabaseMessageConsumer:\r\n    \"\"\"获取全局消费者实例\"\"\"\r\n    global _global_consumer\r\n    if _global_consumer is None:\r\n        _global_consumer = DatabaseMessageConsumer()\r\n    return _global_consumer\r\n\r\n\r\ndef start_global_consumer():\r\n    \"\"\"启动全局消费者\"\"\"\r\n    consumer = get_consumer()\r\n    consumer.start_consumer()\r\n\r\n\r\ndef stop_global_consumer():\r\n    \"\"\"停止全局消费者\"\"\"\r\n    global _global_consumer\r\n    if _global_consumer:\r\n        _global_consumer.stop_consumer()\r\n        _global_consumer = None\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/services/mq_consumer.py b/app/services/mq_consumer.py
--- a/app/services/mq_consumer.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/services/mq_consumer.py	(date 1754012749081)
@@ -18,6 +18,7 @@
 from pika.exceptions import AMQPChannelError, AMQPConnectionError
 from psycopg2.extras import RealDictCursor
 
+from app.config.enums import ExecutionStatus
 from app.config.settings import settings
 from app.schemas.mq_message import (
     AppendLogMessage,
@@ -139,11 +140,11 @@
             self.connection = None
             raise
 
-    def update_task_status(self, ticket_id: str, status: str, **kwargs) -> bool:
+    def update_task_status(self, ticket_id: str, status: ExecutionStatus, **kwargs) -> bool:
         """更新任务状态"""
         try:
             set_clauses = ["status = %s", "updated_at = %s"]
-            params = [status, get_shanghai_now()]
+            params = [status.value, get_shanghai_now()]
 
             # 处理额外的字段更新，确保序列化字典类型
             for field, value in kwargs.items():
@@ -237,7 +238,7 @@
         """更新任务错误"""
         try:
             sql = "UPDATE task SET error_message = %s, status = %s, updated_at = %s WHERE ticket_id = %s"
-            self.execute_query(sql, (error_message, "FAILURE", get_shanghai_now(), ticket_id))
+            self.execute_query(sql, (error_message, ExecutionStatus.FAILED.value, get_shanghai_now(), ticket_id))
 
             logger.debug(f"更新任务错误成功: {ticket_id}")
             return True
@@ -728,11 +729,11 @@
             data = UpdateStatusMessage(**message.data)
             now = get_shanghai_now()
             kwargs = {}
-            if data.status == "SUCCESS":
+            if data.status == ExecutionStatus.SUCCESS:
                 kwargs.update({"result": data.result_or_error, "error_message": None, "completed_at": now})
-            elif data.status == "RUNNING":
+            elif data.status == ExecutionStatus.RUNNING:
                 kwargs.update({"replica_id": settings.replica_id, "started_at": now})
-            else:  # FAILURE or CANCELLED
+            else:  # FAILED or CANCELLED
                 kwargs.update(
                     {
                         "result": None,
@@ -749,20 +750,18 @@
             # 更新数据库状态
             db_update_success = self._db_operator.update_task_status(message.ticket_id, data.status, **kwargs)
 
-            # 对于最终状态（SUCCESS, FAILURE, CANCELLED），发送回调到主服务
-            if data.status in ["SUCCESS", "FAILURE", "CANCELLED"] and db_update_success:
+            # 对于最终状态（SUCCESS, FAILURE），发送回调到主服务
+            if data.status in [ExecutionStatus.SUCCESS, ExecutionStatus.FAILED] and db_update_success:
                 try:
                     callback_service = get_callback_service()
 
                     # 准备回调参数
-                    callback_kwargs = {"ticket_id": message.ticket_id, "status": data.status}
+                    callback_kwargs = {"type": "task", "ticket_id": message.ticket_id, "status": data.status.value}
 
-                    if data.status == "FAILURE":
+                    if data.status == ExecutionStatus.FAILED:
                         callback_kwargs["error_message"] = (
                             str(data.result_or_error) if data.result_or_error else "执行失败"
                         )
-                    elif data.status == "CANCELLED":
-                        callback_kwargs["error_message"] = "任务被取消"
 
                     # 发送回调（使用同步方式，因为我们在线程池中）
                     callback_success = callback_service.send_task_callback_sync(**callback_kwargs)
@@ -775,7 +774,7 @@
 
                 except Exception as callback_error:
                     logger.warning(
-                        f"发送回调时发生异常: {message.ticket_id}, 错误: {str(callback_error)} (不影响任务执行)"
+                        f"发送任务回调时发生异常: {message.ticket_id}, 错误: {str(callback_error)} (不影响任务执行)"
                     )
                     # 不影响主流程，回调失败不应该导致消息处理失败
 
Index: app/config/settings.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\n应用配置设置\r\n\r\n包含所有环境变量和配置项的管理\r\n\"\"\"\r\n\r\nimport os\r\nimport socket\r\n\r\nfrom dotenv import load_dotenv\r\nfrom pydantic_settings import BaseSettings\r\n\r\n\r\nclass Settings(BaseSettings):\r\n    \"\"\"应用配置类\"\"\"\r\n\r\n    # ==================== 基础配置 ====================\r\n\r\n    # 应用信息\r\n    app_title: str = \"WebUI Agent Executor\"\r\n    app_version: str = \"1.0.0\"\r\n\r\n    # 服务器配置\r\n    host: str = \"0.0.0.0\"\r\n    port: int = 8080\r\n\r\n    # 是否启用调试模式\r\n    debug: bool = False\r\n\r\n    # 禁用OpenAPI\r\n    openapi_url: str | None = None\r\n\r\n    # 日志配置\r\n    log_dir: str = \"logs\"\r\n\r\n    # 密钥配置\r\n    keys_dir: str = \"keys\"\r\n\r\n    # 主服务地址\r\n    webui_agent_url: str = \"http://127.0.0.1:8000/api/v1/webui\"\r\n\r\n    # ==================== 副本配置 ====================\r\n\r\n    # 获取当前副本的唯一标识（Kubernetes 中是 Pod 名称）\r\n    replica_id: str = os.getenv(\"HOSTNAME\") or socket.gethostname()\r\n\r\n    # ==================== 数据库配置 ====================\r\n\r\n    # 数据库连接字符串 (PostgreSQL格式: postgres://user:password@host:port/database)\r\n    database_url: str = \"\"\r\n\r\n    # 数据库时区\r\n    database_timezone: str = \"Asia/Shanghai\"\r\n\r\n    # ==================== LLM 配置 ====================\r\n\r\n    # LLM API 配置\r\n    openai_api_key: str = \"\"\r\n    openai_base_url: str = \"https://api.siliconflow.cn/v1\"\r\n    openai_model: str = \"Qwen/Qwen2.5-VL-32B-Instruct\"\r\n    openai_temperature: float = 0.0\r\n    openai_max_tokens: int = 16384\r\n\r\n    # Token管理配置\r\n    enable_token_truncation: bool = True  # 是否启用token截断功能\r\n    max_input_tokens: int = 24000  # 最大输入token数量（DeepSeek安全限制）\r\n    token_buffer: int = 576  # 为输出预留的token空间\r\n\r\n    # Planner LLM 配置\r\n    planner_llm_api_key: str = \"\"\r\n    planner_llm_base_url: str = \"http://************:20008/v1\"\r\n    planner_llm_model: str = \"Qwen3-32B\"\r\n    planner_llm_temperature: float = 0.0\r\n    planner_llm_max_tokens: int = 16384\r\n\r\n    # ==================== 浏览器配置 ====================\r\n\r\n    # 浏览器远程URL\r\n    selenium_remote_url: str = \"http://127.0.0.1:4444\"\r\n\r\n    # 浏览器窗口大小\r\n    browser_window_width: int = 1920\r\n    browser_window_height: int = 1080\r\n\r\n    # # 是否启用无头模式\r\n    browser_headless: bool = False\r\n\r\n    # 是否启用视频录制\r\n    video_recording_enabled: bool = True\r\n\r\n    # 视频录制保存目录\r\n    video_recording_dir: str = \"recordings/video\"\r\n\r\n    # 视频录制分辨率\r\n    video_recording_width: int = 1280\r\n    video_recording_height: int = 720\r\n\r\n    # ==================== Browser Use 配置 ====================\r\n\r\n    # 是否启用视觉\r\n    use_vision: bool = False\r\n\r\n    # 是否启用视觉用于Planner\r\n    use_vision_for_planner: bool = False\r\n\r\n    # 是否启用Planner\r\n    use_planner: bool = False\r\n\r\n    # Planner间隔\r\n    planner_interval: int = 1\r\n\r\n    # 消息上下文\r\n    message_context: str = \"\"\r\n\r\n    # 扩展系统提示词\r\n    extend_system_message: str = \"\"\"\r\n    REMEMBER the most important RULES for this task:\r\n    1. Always use the `search_baidu` action instead of `search_google` action!!!\r\n    2. Never use `login_ecloud` action!!!\r\n\r\n    你是WebUI自动化测试专家，专门使用Browser-Use框架执行Web界面测试。你需要：\r\n\r\n    \uD83E\uDDE0 思考方式：\r\n    - 使用中文进行逻辑思考和分析\r\n    - 对每个操作进行详细的计划和验证\r\n    - 遇到问题时提供智能的解决方案\r\n\r\n    Browser-Use支持的所有动作格式（严格按照以下JSON格式执行）：\r\n\r\n    1. 导航动作：\r\n       - 访问URL: {\"go_to_url\": {\"url\": \"https://example.com\", \"new_tab\": false}}\r\n       - 新标签页打开: {\"go_to_url\": {\"url\": \"https://example.com\", \"new_tab\": true}}\r\n       - 返回上一页: {\"go_back\": {}}\r\n       - 等待: {\"wait\": {\"seconds\": 3}}\r\n\r\n    2. 页面交互动作：\r\n       - 点击元素: {\"click_element_by_index\": {\"index\": 数字}}\r\n       - 输入文本: {\"input_text\": {\"index\": 数字, \"text\": \"文本内容\"}}\r\n       - 发送按键: {\"send_keys\": {\"keys\": \"Enter\"}}\r\n       - 上传文件: {\"upload_file\": {\"index\": 数字, \"path\": \"文件路径\"}}\r\n\r\n    3. 滚动动作（重要 - 必须使用正确格式）：\r\n       - 向下滚动: {\"scroll\": {\"down\": true, \"num_pages\": 3}}\r\n       - 向上滚动: {\"scroll\": {\"down\": false, \"num_pages\": 2}}\r\n       - 滚动到文本: {\"scroll_to_text\": {\"text\": \"目标文本\"}}\r\n\r\n    4. 标签页管理动作（重要 - 必须使用page_id参数）：\r\n       - 切换标签页: {\"switch_tab\": {\"page_id\": 数字}}\r\n       - 最佳实践：切换后必须添加等待步骤确保标签页切换完成\r\n       - 建议等待时间：1-2秒，确保页面状态稳定\r\n       - 验证方法：检查当前活动标签页是否为目标页面\r\n       - 关闭标签页: {\"close_tab\": {\"page_id\": 数字}}\r\n       - 最佳实践：关闭后必须添加等待步骤确保标签页关闭完成\r\n       - 建议等待时间：1-2秒，确保DOM更新和资源释放\r\n       - 验证方法：确认目标标签页已从标签页列表中移除\r\n\r\n    5. 数据提取动作：\r\n       - 提取结构化数据: {\"extract_structured_data\": {\"query\": \"提取搜索结果列表\", \"extract_links\": true}}\r\n\r\n    6. 任务完成：\r\n       - 完成任务: {\"done\": {\"text\": \"任务完成描述\", \"success\": true}}\r\n\r\n    \uD83D\uDEA8 关键格式要求：\r\n    - 滚动动作必须使用: {\"scroll\": {\"down\": true/false, \"num_pages\": 数字}} 格式\r\n    - 向下滚动用down: true，向上滚动用down: false\r\n    - 标签页操作必须使用page_id参数，不是index参数\r\n    - 标签页操作时序要求：每次switch_tab或close_tab操作后必须添加wait步骤\r\n    - 标准操作模式：操作 → 等待 → 验证 → 下一步，确保页面状态稳定\r\n    - 响应必须是有效的JSON格式，不要包含markdown代码块\r\n    - 不要添加thinking字段或其他额外字段\r\n\r\n    \uD83D\uDCDD 响应格式示例：\r\n    {\r\n    \"evaluation_previous_goal\": \"评估上一步操作的执行结果\",\r\n    \"memory\": \"记录当前测试进展和重要发现\",\r\n    \"next_goal\": \"明确下一步要执行的具体目标\",\r\n    \"action\": [{\"go_to_url\": {\"url\": \"https://www.baidu.com\", \"new_tab\": false}}]\r\n    }\r\n\r\n    \uD83D\uDCDD 多标签页操作示例：\r\n    {\r\n    \"evaluation_previous_goal\": \"已成功打开百度首页\",\r\n    \"memory\": \"当前在百度首页，准备测试多标签页功能\",\r\n    \"next_goal\": \"在新标签页打开必应搜索，然后测试标签页切换和关闭\",\r\n    \"action\": [\r\n       {\"go_to_url\": {\"url\": \"https://www.bing.com\", \"new_tab\": true}},\r\n       {\"wait\": {\"seconds\": 2}},\r\n       {\"switch_tab\": {\"page_id\": 0}},\r\n       {\"wait\": {\"seconds\": 1}},\r\n       {\"switch_tab\": {\"page_id\": 1}},\r\n       {\"wait\": {\"seconds\": 1}},\r\n       {\"close_tab\": {\"page_id\": 1}},\r\n       {\"wait\": {\"seconds\": 2}}\r\n    ]\r\n    }\r\n\r\n    \uD83C\uDFAF WebUI测试场景覆盖：\r\n\r\n    【基础交互场景】\r\n    - 页面导航：URL访问、页面加载验证、重定向处理、多标签页管理\r\n    - 元素交互：点击操作、文本输入、表单提交、文件上传、下拉选择\r\n    - 视窗控制：页面滚动、元素定位、可见性管理、固定元素处理\r\n\r\n    【复杂业务场景】\r\n    - 用户认证：多种登录方式、状态保持、安全验证、异常处理\r\n    - 数据操作：内容提取、数据验证、动态加载、结构化处理\r\n    - 流程测试：端到端业务流程、状态依赖管理、数据一致性验证\r\n\r\n    【技术挑战场景】\r\n    - 动态内容：AJAX加载、实时更新、异步操作、延迟渲染\r\n    - 性能优化：加载时间监控、资源管理、内存控制、网络适应\r\n    - 异常处理：错误恢复、重试机制、状态回滚、容错设计\r\n\r\n    \uD83C\uDFAF 核心验证重点：\r\n    - \uD83E\uDDE0 **中文智能思考验证**：展示DeepSeek的中文逻辑分析能力和智能决策过程\r\n    - � **动作格式标准验证**：确保所有Browser-Use动作严格遵循JSON格式规范\r\n    - � **操作连续性验证**：验证多步骤操作间的状态保持和数据传递准确性\r\n    - ⚡ **异常恢复能力验证**：测试重试机制、错误处理和智能恢复策略的有效性\r\n    - ✅ **结果准确性验证**：确认操作执行结果与预期目标的完全一致性\r\n\r\n    \uD83D\uDD27 智能操作原则：\r\n    - 状态优先：操作前检查元素当前状态，已达目标则跳过\r\n    - 错误优先：优先检测和处理错误状态，避免无效操作\r\n    - 智能判断：根据元素实际状态决定操作策略，避免重复执行\r\n\r\n    \uD83D\uDCCB 操作执行协议：\r\n    - 可见性要求：元素必须在视窗内可见≥80%且无遮挡\r\n    - 交互性验证：确认元素非disabled状态且可接收用户操作\r\n    - 状态一致性：操作后验证元素状态变化符合预期\r\n\r\n    ⚠\uFE0F 异常处理机制：\r\n    - 重试策略：最多3次重试，间隔1.5秒，失败后切换定位方法\r\n    - 定位策略：XPath文本定位 → 相邻元素回溯 → 视觉坐标定位\r\n    - 错误记录：截图标注 + DOM快照 + 控制台日志\r\n    - 标签页操作特殊处理：\r\n    - 切换失败：验证page_id有效性 → 重新获取标签页列表 → 重试切换操作\r\n    - 关闭失败：检查标签页状态 → 强制刷新标签页列表 → 重试关闭操作\r\n    - 状态验证失败：等待额外2秒 → 重新检查标签页状态 → 必要时回滚操作\r\n\r\n    ✅ 验证标准：\r\n    - 正面验证：元素属性变更 + 网络请求完成 + 页面状态更新\r\n    - 负面验证：无错误元素 + 无控制台异常 + 无权限问题\r\n\r\n    \uD83D\uDCCA 状态管理清单：\r\n    - 可跳过：目标状态已达成（表单已填写、选项已选中、加载已完成）\r\n    - 必须中断：致命错误状态（权限拒绝、系统错误、连续失败）\r\n    \"\"\"\r\n\r\n    # 扩展Planner系统提示词\r\n    extend_planner_system_message: str = \"\"\r\n\r\n    # playwright延缓操作时间（毫秒）\r\n    slow_mo: int = 0\r\n\r\n    # 文件系统路径\r\n    file_system_dir: str = \"recordings/file\"\r\n\r\n    # 截图保存目录\r\n    screenshot_dir: str = \"recordings/screenshot\"\r\n\r\n    # 是否启用匿名化遥测\r\n    anonymized_telemetry: bool = False\r\n\r\n    # ==================== MinIO 对象存储配置 ====================\r\n\r\n    # MinIO服务端点URL\r\n    minio_endpoint: str = \"192.168.215.2:9000\"\r\n\r\n    # MinIO访问密钥\r\n    minio_access_key: str = \"minioadmin\"\r\n\r\n    # MinIO秘密密钥\r\n    minio_secret_key: str = \"\"\r\n\r\n    # MinIO存储桶名称\r\n    minio_bucket_name: str = \"webui-agent-recordings\"\r\n\r\n    # 是否使用HTTPS连接MinIO\r\n    minio_secure: bool = False\r\n\r\n    # MinIO区域设置\r\n    minio_region: str = \"us-east-1\"\r\n\r\n    # 文件URL过期时间（秒）\r\n    minio_url_expiry: int = 3600\r\n\r\n    # 是否启用MinIO存储（如果为False，则使用本地存储）\r\n    minio_enabled: bool = False\r\n\r\n    # ==================== RabbitMQ 配置 ====================\r\n\r\n    # RabbitMQ主机\r\n    rabbitmq_host: str = \"localhost\"\r\n\r\n    # RabbitMQ端口\r\n    rabbitmq_port: int = 5672\r\n\r\n    # RabbitMQ用户名\r\n    rabbitmq_username: str = \"admin\"\r\n\r\n    # RabbitMQ密码\r\n    rabbitmq_password: str = \"\"\r\n\r\n    # RabbitMQ虚拟主机\r\n    rabbitmq_vhost: str = \"/\"\r\n\r\n    # 数据库操作交换机名称\r\n    rabbitmq_db_exchange: str = \"webui_agent_db_operations\"\r\n\r\n    # 数据库操作队列名称\r\n    rabbitmq_db_queue: str = \"webui_agent_db_queue\"\r\n\r\n    # 路由键\r\n    rabbitmq_routing_key: str = \"db_operation\"\r\n\r\n    # 消息持久化\r\n    rabbitmq_durable: bool = True\r\n\r\n    # 最大重试次数\r\n    rabbitmq_max_retry_times: int = 3\r\n\r\n    # 心跳间隔（秒）\r\n    rabbitmq_heartbeat: int = 600\r\n\r\n    # 消息处理超时时间（秒）\r\n    rabbitmq_message_timeout: int = 60\r\n\r\n    # 死信队列配置\r\n    rabbitmq_dead_letter_exchange: str = \"webui_agent_dead_letter\"\r\n    rabbitmq_dead_letter_queue: str = \"webui_agent_dead_letter_queue\"\r\n    rabbitmq_dead_letter_routing_key: str = \"dead_letter\"\r\n\r\n    # ==================== 任务配置 ====================\r\n\r\n    # 任务超时时间（分钟）\r\n    task_timeout_minutes: int = 30\r\n\r\n    # 进程终止等待时间（秒）\r\n    process_terminate_timeout: float = 3.0\r\n    process_kill_timeout: float = 1.0\r\n\r\n    # 监控检查间隔（秒）\r\n    monitor_check_interval: float = 5.0\r\n\r\n    # 浏览器会话监控检查间隔（秒）\r\n    browser_session_check_interval: float = 1.0\r\n\r\n    class Config:\r\n        # 指定要加载的环境变量文件\r\n        env_file = \".env\"\r\n        # 指定环境变量文件的编码\r\n        env_file_encoding = \"utf-8\"\r\n        # 不区分大小写，这样可以匹配更多环境变量\r\n        case_sensitive = False\r\n        # 忽略模型中未定义的额外字段\r\n        extra = \"ignore\"\r\n\r\n\r\ndef get_settings() -> Settings:\r\n    \"\"\"获取设置，每次都重新加载.env文件以确保获取最新配置\"\"\"\r\n    load_dotenv(override=True)\r\n    return Settings()\r\n\r\n\r\n# 全局配置实例\r\nsettings = get_settings()\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/config/settings.py b/app/config/settings.py
--- a/app/config/settings.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/config/settings.py	(date 1753788869101)
@@ -335,6 +335,9 @@
     # 任务超时时间（分钟）
     task_timeout_minutes: int = 30
 
+    # 脚本执行超时时间（秒）
+    script_timeout_seconds: int = 900  # 15分钟
+
     # 进程终止等待时间（秒）
     process_terminate_timeout: float = 3.0
     process_kill_timeout: float = 1.0
Index: app/models/task.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\r\n任务数据库模型\r\n\r\n定义任务在数据库中的结构和字段\r\n\"\"\"\r\n\r\nfrom tortoise import fields\r\nfrom tortoise.models import Model\r\n\r\n\r\nclass Task(Model):\r\n    \"\"\"\r\n    任务数据库模型\r\n\r\n    这个模型存储所有副本的任务信息，实现跨副本的任务状态共享：\r\n    - 任务创建时记录在哪个副本上创建\r\n    - 任务执行时记录在哪个副本上运行\r\n    - 任务终止时可以跨副本进行控制\r\n    \"\"\"\r\n\r\n    # 自增主键ID\r\n    id = fields.IntField(pk=True, description=\"主键ID\")\r\n\r\n    # 任务唯一标识符（原来的id字段，保持业务逻辑不变）\r\n    ticket_id = fields.CharField(max_length=50, unique=True, description=\"任务唯一标识符\")\r\n\r\n    # 任务状态：PENDING(等待) -> RUNNING(运行中) -> SUCCESS(成功)/FAILURE(失败)/CANCELLED(取消)\r\n    status = fields.CharField(max_length=20, default=\"PENDING\", index=True, description=\"任务状态\")\r\n\r\n    # 用户提交的任务描述\r\n    task_description = fields.TextField(description=\"用户提交的任务描述\")\r\n\r\n    # 任务执行步骤日志\r\n    step_log = fields.TextField(null=True, description=\"任务执行步骤日志\")\r\n\r\n    # 任务执行成功时的结果（JSON 格式存储历史记录）\r\n    result = fields.JSONField(null=True, description=\"任务执行结果\")\r\n\r\n    # 任务执行失败时的错误信息\r\n    error_message = fields.TextField(null=True, description=\"任务执行失败时的错误信息\")\r\n\r\n    # 处理该任务的副本ID（哪个 Pod 在执行这个任务）\r\n    replica_id = fields.CharField(max_length=100, null=True, index=True, description=\"处理该任务的副本ID\")\r\n\r\n    # Selenium会话ID\r\n    selenium_session_id = fields.CharField(max_length=50, null=True, description=\"Selenium会话ID\")\r\n\r\n    # 任务创建时间\r\n    created_at = fields.DatetimeField(auto_now_add=True, index=True, description=\"任务创建时间\")\r\n\r\n    # 任务最后更新时间\r\n    updated_at = fields.DatetimeField(auto_now=True, index=True, description=\"任务最后更新时间\")\r\n\r\n    # 任务开始执行时间（用于超时检查）\r\n    started_at = fields.DatetimeField(null=True, description=\"任务开始执行时间\")\r\n\r\n    # 任务执行完成时间\r\n    completed_at = fields.DatetimeField(null=True, description=\"任务执行完成时间\")\r\n\r\n    # ==================== 文件存储相关字段 ====================\r\n\r\n    # 视频录制文件路径列表（JSON格式存储多个视频文件路径）\r\n    video_file_paths = fields.JSONField(null=True, description=\"视频录制文件路径列表\")\r\n\r\n    # 截图文件路径列表（JSON格式存储多个截图路径）\r\n    screenshot_file_paths = fields.JSONField(null=True, description=\"截图文件路径列表\")\r\n\r\n    # 下载文件路径列表（JSON格式存储多个下载文件路径）\r\n    download_file_paths = fields.JSONField(null=True, description=\"下载文件路径列表\")\r\n\r\n    class Meta:\r\n        table = \"task\"\r\n        table_description = \"任务数据库模型\"\r\n        indexes = [\r\n            # 复合索引：副本ID和状态\r\n            (\"replica_id\", \"status\"),\r\n            (\"status\",),\r\n            (\"created_at\",),\r\n            (\"updated_at\",),\r\n        ]\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/models/task.py b/app/models/task.py
--- a/app/models/task.py	(revision 4840ab8b305fa5f55934656ee4e78aae89462922)
+++ b/app/models/task.py	(date 1754011454855)
@@ -7,6 +7,8 @@
 from tortoise import fields
 from tortoise.models import Model
 
+from app.config.enums import ExecutionStatus
+
 
 class Task(Model):
     """
@@ -25,7 +27,7 @@
     ticket_id = fields.CharField(max_length=50, unique=True, description="任务唯一标识符")
 
     # 任务状态：PENDING(等待) -> RUNNING(运行中) -> SUCCESS(成功)/FAILURE(失败)/CANCELLED(取消)
-    status = fields.CharField(max_length=20, default="PENDING", index=True, description="任务状态")
+    status = fields.CharEnumField(ExecutionStatus, default=ExecutionStatus.PENDING, description="任务状态")
 
     # 用户提交的任务描述
     task_description = fields.TextField(description="用户提交的任务描述")
@@ -40,16 +42,16 @@
     error_message = fields.TextField(null=True, description="任务执行失败时的错误信息")
 
     # 处理该任务的副本ID（哪个 Pod 在执行这个任务）
-    replica_id = fields.CharField(max_length=100, null=True, index=True, description="处理该任务的副本ID")
+    replica_id = fields.CharField(max_length=100, null=True, description="处理该任务的副本ID")
 
     # Selenium会话ID
     selenium_session_id = fields.CharField(max_length=50, null=True, description="Selenium会话ID")
 
     # 任务创建时间
-    created_at = fields.DatetimeField(auto_now_add=True, index=True, description="任务创建时间")
+    created_at = fields.DatetimeField(auto_now_add=True, description="任务创建时间")
 
     # 任务最后更新时间
-    updated_at = fields.DatetimeField(auto_now=True, index=True, description="任务最后更新时间")
+    updated_at = fields.DatetimeField(auto_now=True, description="任务最后更新时间")
 
     # 任务开始执行时间（用于超时检查）
     started_at = fields.DatetimeField(null=True, description="任务开始执行时间")
@@ -71,10 +73,3 @@
     class Meta:
         table = "task"
         table_description = "任务数据库模型"
-        indexes = [
-            # 复合索引：副本ID和状态
-            ("replica_id", "status"),
-            ("status",),
-            ("created_at",),
-            ("updated_at",),
-        ]
