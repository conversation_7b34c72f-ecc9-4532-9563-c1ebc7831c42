"""
回调服务

用于向主服务发送任务执行结果的回调通知
"""

import requests

from app.config.settings import settings
from app.utils.log import logger


class CallbackService:
    """回调服务类"""

    def __init__(self):
        self.timeout = 10.0  # 10秒超时

    def send_task_callback(
        self,
        type: str,
        ticket_id: str,
        status: str,
        error_message: str | None = None,
    ) -> bool:
        """
        发送任务执行结果回调

        Args:
            type: 回调类型 (task, script)
            ticket_id: 任务票据ID
            status: 任务状态 (SUCCESS, FAILURE)
            error_message: 错误信息（仅当状态为FAILURE时）

        Returns:
            bool: 回调是否成功发送
        """
        try:
            # 构建回调URL
            callback_url = f"{settings.webui_agent_url}/{type}/callback"

            # 构建回调数据
            callback_data = {
                "ticket_id": ticket_id,
                "status": status,
            }

            # 添加可选字段
            if error_message:
                callback_data["error_message"] = error_message

            logger.info(f"发送任务回调: {callback_url}, 数据: {callback_data}")

            # 发送回调请求
            response = requests.post(
                callback_url, json=callback_data, headers={"Content-Type": "application/json"}, timeout=self.timeout
            )

            # 记录响应详情用于调试
            logger.debug(f"回调响应状态码: {response.status_code}")
            logger.debug(f"回调响应内容: {response.text}")

            # 检查响应
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    code = response_data.get("code", 5000)
                    response_message = response_data.get("message", "未知错误")

                    if code == 2000:
                        logger.info(f"任务回调发送成功: {ticket_id}, 状态: {status}, 消息: {response_message}")
                        return True
                    else:
                        logger.error(f"任务回调处理失败: {ticket_id}, 状态: {status}, 消息: {response_message}")
                        return False
                except (ValueError, TypeError):
                    # 如果响应不是有效的JSON，但HTTP状态码是200，则认为成功
                    logger.info(f"任务回调发送成功: {ticket_id}, 状态: {status} (响应不是JSON格式)")
                    return True
            else:
                logger.error(f"任务回调请求失败: {ticket_id}, HTTP状态: {response.status_code}, 响应: {response.text}")
                return False

        except requests.exceptions.Timeout:
            logger.error(f"任务回调请求超时: {ticket_id}")
            return False
        except requests.exceptions.RequestException as e:
            logger.error(f"任务回调请求异常: {ticket_id}, 错误: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"任务回调发送异常: {ticket_id}, 错误: {str(e)}")
            return False

    def send_task_callback_sync(
        self,
        type: str,
        ticket_id: str,
        status: str,
        error_message: str | None = None,
    ) -> bool:
        """
        同步方式发送任务执行结果回调

        这里直接调用同步方法，为了保持接口一致性
        """
        return self.send_task_callback(type, ticket_id, status, error_message)


# 全局回调服务实例
_global_callback_service: CallbackService | None = None


def get_callback_service() -> CallbackService:
    """获取全局回调服务实例"""
    global _global_callback_service
    if _global_callback_service is None:
        _global_callback_service = CallbackService()
    return _global_callback_service
